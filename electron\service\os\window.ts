import path from 'path';
import {BrowserWindow, Notification, app as electronApp} from 'electron';
import {getMainWindow} from 'ee-core/electron';
import Ps, {isProd, getBaseDir} from 'ee-core/ps';
import {getConfig} from 'ee-core/config';
import {isFileProtocol} from 'ee-core/utils';
import {logger} from 'ee-core/log';
import CoreElectronWindow from 'ee-core/electron/window';
import Window from "ee-core/electron/window";
import UtilsIs from "ee-core/utils/is";
import fs from "fs";
import cp from "child_process";
import { ipcMain } from "electron";
import { windowStateService } from "./windowState";
import { t, getCurrentLanguage } from "../../data/i18n/i18n";

/**
 * Window
 * @class
 */
class WindowService {
  myNotification: Notification | null;
  windows: { [key: string]: BrowserWindow };

  constructor() {
    this.myNotification = null;
    this.windows = {};

    // 初始化窗口状态管理
    this.initializeWindowStateManagement();
  }

  /**
   * 初始化窗口状态管理
   */
  private initializeWindowStateManagement(): void {
    try {
      windowStateService.initialize();

      // 开始定时保存窗口状态（每30秒）
      windowStateService.startPeriodicSave(30000);

      logger.info(
        `[WindowService] ${t("services.window.windowStateInitialized")}`
      );
    } catch (error) {
      logger.error(
        `[WindowService] ${t("services.window.windowStateInitFailed")}:`,
        error
      );
    }
  }

  /**
   * Create a new window
   */
  createWindow(args: {
    type: string;
    content: string;
    windowName: string;
    windowTitle: string;
  }): number {
    const { type, content, windowName, windowTitle } = args;
    let contentUrl: string = "";
    if (type == "html") {
      contentUrl = path.join("file://", getBaseDir(), content);
    } else if (type == "web") {
      contentUrl = content;
    } else if (type == "vue") {
      let addr = "http://localhost:8080";
      if (isProd()) {
        const { mainServer } = getConfig();
        if (
          mainServer &&
          mainServer.protocol &&
          isFileProtocol(mainServer.protocol)
        ) {
          addr =
            mainServer.protocol + path.join(getBaseDir(), mainServer.indexPath);
        }
      }

      contentUrl = addr + content;
    }

    logger.info("[createWindow] url: ", contentUrl);
    const opt = {
      title: windowTitle,
      x: 10,
      y: 10,
      width: 980,
      height: 650,
      frame: false,
      maximizable: true,
      movable: true,
      webPreferences: {
        contextIsolation: false,
        nodeIntegration: true,
      },
    };
    const win = new BrowserWindow(opt);
    const winContentsId = win.webContents.id;
    win.loadURL(contentUrl);
    // win.webContents.openDevTools(); // 调试时自动打开DevTools，现已注释
    this.windows[windowName] = win;

    ipcMain.on("maximize-window", () => this.maximizeWindow());

    return winContentsId;
  }

  /**
   * Get window contents id
   */
  getWCid(args: { windowName: string }): number {
    const { windowName } = args;
    let win: BrowserWindow;
    if (windowName == "main") {
      win = getMainWindow();
    } else {
      win = this.windows[windowName];
    }

    return win.webContents.id;
  }

  /**
   * Realize communication between two windows through the transfer of the main process
   */
  communicate(args: { receiver: string; content: any }): void {
    const { receiver, content } = args;
    if (receiver == "main") {
      const win = getMainWindow();
      win.webContents.send("controller/os/window2ToWindow1", content);
    } else if (receiver == "window2") {
      const win = this.windows[receiver];
      win.webContents.send("controller/os/window1ToWindow2", content);
    }
  }

  /**
   * createNotification
   */
  createNotification(options: any, event: any): void {
    const channel = "controller/os/sendNotification";
    this.myNotification = new Notification(options);

    if (options.clickEvent) {
      this.myNotification.on("click", () => {
        let data = {
          type: "click",
          msg: t("services.window.clickNotification"),
        };
        event.reply(`${channel}`, data);
      });
    }

    if (options.closeEvent) {
      this.myNotification.on("close", () => {
        let data = {
          type: "close",
          msg: t("services.window.closeNotification"),
        };
        event.reply(`${channel}`, data);
      });
    }

    this.myNotification.show();
  }

  /**
   * closeWindow
   */
  async closeWindow(type) {
    const mainWindow = CoreElectronWindow.getMainWindow();
    if (type === "1") {
      // 直接退出
      await electronApp.quit();
    } else if (type === "2") {
      // 最小化托盘
      mainWindow.hide();
    }
  }

  /**
   * maximizeWindow
   */
  async maximizeWindow() {
    const mainWindow = CoreElectronWindow.getMainWindow();
    if (mainWindow.isFullScreen()) {
      mainWindow.setFullScreen(false);
    } else if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }

  /**
   * minimizeWindow
   */
  async minimizeWindow() {
    const mainWindow = CoreElectronWindow.getMainWindow();
    mainWindow.minimize();
  }

  /**
   * 拖拽窗口
   */
  async dragWindow() {
    const mainWindow = CoreElectronWindow.getMainWindow();
    if (mainWindow) {
      // 确保窗口可移动
      mainWindow.setMovable(true);
      // 设置窗口为可拖拽状态
      mainWindow.setWindowButtonVisibility(true);
      // 确保窗口在最前面
      mainWindow.moveTop();
    }
  }

  /**
   * 保存当前窗口状态
   */
  async saveWindowState() {
    try {
      windowStateService.saveWindowState();
      logger.info(`[WindowService] ${t("services.window.windowStateSaved")}`);
    } catch (error) {
      logger.error(
        `[WindowService] ${t("services.window.saveWindowStateFailed")}:`,
        error
      );
    }
  }

  /**
   * 恢复窗口状态
   */
  async restoreWindowState() {
    try {
      windowStateService.restoreWindowState();
      logger.info(
        `[WindowService] ${t("services.window.windowStateRestored")}`
      );
    } catch (error) {
      logger.error(
        `[WindowService] ${t("services.window.restoreWindowStateFailed")}:`,
        error
      );
    }
  }

  async printScreen() {
    let softwarePath = path.join(
      Ps.getExtraResourcesDir(),
      "dll",
      "PrintScr.exe"
    );
    const mainWindow = Window.getMainWindow();
    if (!UtilsIs.windows()) {
      return;
    }
    // 检查程序是否存在
    if (!fs.existsSync(softwarePath)) {
      return;
    }
    mainWindow.hide();
    setTimeout(() => {
      const screen = cp.execFile(softwarePath);
      screen.on("exit", () => {
        mainWindow.show();
        mainWindow.moveTop();
      });
    }, 150);
  }

  /**
   * 获取开发者工具的语言设置
   * @param language 当前应用语言
   * @returns 开发者工具语言代码
   */
  private getDevToolsLocale(language: string): string {
    const localeMap: Record<string, string> = {
      zh: "zh-CN",
      en: "en-US",
      es: "es-ES",
      fr: "fr-FR",
    };
    return localeMap[language] || "zh-CN"; // 默认中文
  }

  async openDevTools() {
    const focusedWindow = BrowserWindow.getFocusedWindow();
    if (focusedWindow?.webContents.isDevToolsOpened()) {
      focusedWindow.webContents.closeDevTools();
    } else {
      // 设置开发者工具为中文
      focusedWindow?.webContents.openDevTools({
        mode: "undocked",
        activate: true,
      });

      // 设置开发者工具语言和功能
      focusedWindow?.webContents.once("devtools-opened", () => {
        const currentLang = getCurrentLanguage();
        const devToolsLocale = this.getDevToolsLocale(currentLang);

        focusedWindow.webContents.devToolsWebContents?.executeJavaScript(`
          // 设置开发者工具语言
          try {
            // 方法1: 通过DevToolsAPI设置语言
            if (window.DevToolsAPI && window.DevToolsAPI.setLocale) {
              window.DevToolsAPI.setLocale('${devToolsLocale}');
            }

            // 方法2: 通过Common.settings设置语言
            if (window.Common && window.Common.settings) {
              window.Common.settings.set('language', '${devToolsLocale}');
            }

            // 方法3: 通过InspectorFrontendHost设置语言
            if (window.InspectorFrontendHost && window.InspectorFrontendHost.setPreference) {
              window.InspectorFrontendHost.setPreference('language', '${devToolsLocale}');
            }

            // 方法4: 直接设置localStorage
            localStorage.setItem('devtools-language', '${devToolsLocale}');
            localStorage.setItem('language', '${devToolsLocale}');

            console.log('开发者工具语言已设置为: ${devToolsLocale}');
          } catch (error) {
            console.warn('设置开发者工具语言失败:', error);
          }

          // 为控制台添加复制粘贴功能
          document.addEventListener('keydown', function(e) {
            // Ctrl+C 复制
            if (e.ctrlKey && e.key === 'c') {
              const selection = window.getSelection();
              if (selection && selection.toString()) {
                navigator.clipboard.writeText(selection.toString()).catch(err => {
                  console.warn('复制失败:', err);
                });
              }
            }

            // Ctrl+V 粘贴
            if (e.ctrlKey && e.key === 'v') {
              const activeElement = document.activeElement;
              if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA' || activeElement.contentEditable === 'true')) {
                navigator.clipboard.readText().then(text => {
                  // 插入粘贴的文本
                  if (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA') {
                    const input = activeElement as HTMLInputElement | HTMLTextAreaElement;
                    const start = input.selectionStart || 0;
                    const end = input.selectionEnd || 0;
                    const value = input.value;
                    input.value = value.substring(0, start) + text + value.substring(end);
                    input.selectionStart = input.selectionEnd = start + text.length;
                  } else if (activeElement.contentEditable === 'true') {
                    document.execCommand('insertText', false, text);
                  }
                }).catch(err => {
                  console.warn('粘贴失败:', err);
                });
              }
            }
          });

          // 添加右键菜单复制粘贴功能
          document.addEventListener('contextmenu', function(e) {
            const selection = window.getSelection();
            const activeElement = document.activeElement;

            // 如果有选中文本或者在可编辑元素上，显示自定义菜单
            if ((selection && selection.toString()) ||
                (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA' || activeElement.contentEditable === 'true'))) {

              // 移除已存在的自定义菜单
              const existingMenu = document.getElementById('custom-context-menu');
              if (existingMenu) {
                existingMenu.remove();
              }

              // 创建自定义右键菜单
              const menu = document.createElement('div');
              menu.id = 'custom-context-menu';
              menu.style.cssText = \`
                position: fixed;
                background: white;
                border: 1px solid #ccc;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                z-index: 10000;
                padding: 4px 0;
                font-size: 12px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              \`;

              // 复制选项
              if (selection && selection.toString()) {
                const copyItem = document.createElement('div');
                copyItem.textContent = '复制';
                copyItem.style.cssText = \`
                  padding: 6px 12px;
                  cursor: pointer;
                  color: #333;
                \`;
                copyItem.onmouseover = () => copyItem.style.backgroundColor = '#f0f0f0';
                copyItem.onmouseout = () => copyItem.style.backgroundColor = 'transparent';
                copyItem.onclick = () => {
                  navigator.clipboard.writeText(selection.toString());
                  menu.remove();
                };
                menu.appendChild(copyItem);
              }

              // 粘贴选项
              if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA' || activeElement.contentEditable === 'true')) {
                const pasteItem = document.createElement('div');
                pasteItem.textContent = '粘贴';
                pasteItem.style.cssText = \`
                  padding: 6px 12px;
                  cursor: pointer;
                  color: #333;
                \`;
                pasteItem.onmouseover = () => pasteItem.style.backgroundColor = '#f0f0f0';
                pasteItem.onmouseout = () => pasteItem.style.backgroundColor = 'transparent';
                pasteItem.onclick = () => {
                  navigator.clipboard.readText().then(text => {
                    if (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA') {
                      const input = activeElement as HTMLInputElement | HTMLTextAreaElement;
                      const start = input.selectionStart || 0;
                      const end = input.selectionEnd || 0;
                      const value = input.value;
                      input.value = value.substring(0, start) + text + value.substring(end);
                      input.selectionStart = input.selectionEnd = start + text.length;
                    } else if (activeElement.contentEditable === 'true') {
                      document.execCommand('insertText', false, text);
                    }
                  });
                  menu.remove();
                };
                menu.appendChild(pasteItem);
              }

              // 定位菜单
              menu.style.left = e.pageX + 'px';
              menu.style.top = e.pageY + 'px';

              document.body.appendChild(menu);

              // 点击其他地方关闭菜单
              const closeMenu = (event) => {
                if (!menu.contains(event.target)) {
                  menu.remove();
                  document.removeEventListener('click', closeMenu);
                }
              };
              setTimeout(() => document.addEventListener('click', closeMenu), 0);

              e.preventDefault();
            }
          });
        `);
      });
    }
  }
}

WindowService.toString = () => '[class WindowService]';
const windowService = new WindowService();

export {
    WindowService,
    windowService
} 