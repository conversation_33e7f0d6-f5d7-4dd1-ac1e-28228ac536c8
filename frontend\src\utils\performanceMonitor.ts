/**
 * 性能监控工具
 * 监控应用启动性能和运行时性能
 */

interface PerformanceMetrics {
  // 启动性能
  domContentLoaded: number;
  firstPaint: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;

  // 资源加载性能
  resourceLoadTime: number;
  jsLoadTime: number;
  cssLoadTime: number;

  // Vue应用性能
  vueAppMountTime: number;
  routerReadyTime: number;
  storeInitTime: number;

  // 缓存性能
  cacheHitRate: number;
  cacheLoadTime: number;
}

class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private startTime = performance.now();
  private observer?: PerformanceObserver;

  constructor() {
    this.initObserver();
    this.measureBasicMetrics();
  }

  /**
   * 初始化性能观察器
   */
  private initObserver(): void {
    if ("PerformanceObserver" in window) {
      this.observer = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          this.handlePerformanceEntry(entry);
        }
      });

      // 观察各种性能指标
      try {
        this.observer.observe({ entryTypes: ["paint", "largest-contentful-paint", "first-input", "layout-shift"] });
      } catch (error) {
        console.warn("Performance observer not supported:", error);
      }
    }
  }

  /**
   * 处理性能条目
   */
  private handlePerformanceEntry(entry: PerformanceEntry): void {
    switch (entry.entryType) {
      case "paint":
        if (entry.name === "first-paint") {
          this.metrics.firstPaint = entry.startTime;
        } else if (entry.name === "first-contentful-paint") {
          this.metrics.firstContentfulPaint = entry.startTime;
        }
        break;

      case "largest-contentful-paint":
        this.metrics.largestContentfulPaint = entry.startTime;
        break;

      case "first-input":
        this.metrics.firstInputDelay = (entry as any).processingStart - entry.startTime;
        break;

      case "layout-shift":
        if (!(entry as any).hadRecentInput) {
          this.metrics.cumulativeLayoutShift = (this.metrics.cumulativeLayoutShift || 0) + (entry as any).value;
        }
        break;
    }
  }

  /**
   * 测量基础性能指标
   */
  private measureBasicMetrics(): void {
    // DOM内容加载完成时间
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () => {
        this.metrics.domContentLoaded = performance.now() - this.startTime;
      });
    } else {
      this.metrics.domContentLoaded = 0;
    }

    // 页面加载完成后测量资源加载时间
    window.addEventListener("load", () => {
      this.measureResourceLoadTime();
    });
  }

  /**
   * 测量资源加载时间
   */
  private measureResourceLoadTime(): void {
    const resources = performance.getEntriesByType("resource") as PerformanceResourceTiming[];

    let totalLoadTime = 0;
    let jsLoadTime = 0;
    let cssLoadTime = 0;

    resources.forEach(resource => {
      const loadTime = resource.responseEnd - resource.startTime;
      totalLoadTime += loadTime;

      if (resource.name.endsWith(".js")) {
        jsLoadTime += loadTime;
      } else if (resource.name.endsWith(".css")) {
        cssLoadTime += loadTime;
      }
    });

    this.metrics.resourceLoadTime = totalLoadTime;
    this.metrics.jsLoadTime = jsLoadTime;
    this.metrics.cssLoadTime = cssLoadTime;
  }

  /**
   * 记录Vue应用挂载时间
   */
  recordVueAppMountTime(): void {
    this.metrics.vueAppMountTime = performance.now() - this.startTime;
    console.log(`🚀 Vue应用挂载完成: ${this.metrics.vueAppMountTime.toFixed(2)}ms`);
  }

  /**
   * 记录路由准备时间
   */
  recordRouterReadyTime(): void {
    this.metrics.routerReadyTime = performance.now() - this.startTime;
    console.log(`🛣️ 路由准备完成: ${this.metrics.routerReadyTime.toFixed(2)}ms`);
  }

  /**
   * 记录状态管理初始化时间
   */
  recordStoreInitTime(): void {
    this.metrics.storeInitTime = performance.now() - this.startTime;
    console.log(`🏪 状态管理初始化完成: ${this.metrics.storeInitTime.toFixed(2)}ms`);
  }

  /**
   * 记录缓存性能
   */
  recordCachePerformance(hitRate: number, loadTime: number): void {
    this.metrics.cacheHitRate = hitRate;
    this.metrics.cacheLoadTime = loadTime;
    console.log(`💾 缓存命中率: ${(hitRate * 100).toFixed(1)}%, 加载时间: ${loadTime.toFixed(2)}ms`);
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): PerformanceMetrics {
    return {
      domContentLoaded: this.metrics.domContentLoaded || 0,
      firstPaint: this.metrics.firstPaint || 0,
      firstContentfulPaint: this.metrics.firstContentfulPaint || 0,
      largestContentfulPaint: this.metrics.largestContentfulPaint || 0,
      firstInputDelay: this.metrics.firstInputDelay || 0,
      cumulativeLayoutShift: this.metrics.cumulativeLayoutShift || 0,
      resourceLoadTime: this.metrics.resourceLoadTime || 0,
      jsLoadTime: this.metrics.jsLoadTime || 0,
      cssLoadTime: this.metrics.cssLoadTime || 0,
      vueAppMountTime: this.metrics.vueAppMountTime || 0,
      routerReadyTime: this.metrics.routerReadyTime || 0,
      storeInitTime: this.metrics.storeInitTime || 0,
      cacheHitRate: this.metrics.cacheHitRate || 0,
      cacheLoadTime: this.metrics.cacheLoadTime || 0
    };
  }

  /**
   * 打印性能报告
   */
  printPerformanceReport(): void {
    const report = this.getPerformanceReport();

    console.group("📊 性能监控报告");
    console.log("🎯 核心Web指标:");
    console.log(`  - 首次绘制 (FP): ${report.firstPaint.toFixed(2)}ms`);
    console.log(`  - 首次内容绘制 (FCP): ${report.firstContentfulPaint.toFixed(2)}ms`);
    console.log(`  - 最大内容绘制 (LCP): ${report.largestContentfulPaint.toFixed(2)}ms`);
    console.log(`  - 首次输入延迟 (FID): ${report.firstInputDelay.toFixed(2)}ms`);
    console.log(`  - 累积布局偏移 (CLS): ${report.cumulativeLayoutShift.toFixed(4)}`);

    console.log("⚡ 应用启动性能:");
    console.log(`  - DOM内容加载: ${report.domContentLoaded.toFixed(2)}ms`);
    console.log(`  - Vue应用挂载: ${report.vueAppMountTime.toFixed(2)}ms`);
    console.log(`  - 路由准备: ${report.routerReadyTime.toFixed(2)}ms`);
    console.log(`  - 状态管理初始化: ${report.storeInitTime.toFixed(2)}ms`);

    console.log("📦 资源加载性能:");
    console.log(`  - 总资源加载时间: ${report.resourceLoadTime.toFixed(2)}ms`);
    console.log(`  - JS加载时间: ${report.jsLoadTime.toFixed(2)}ms`);
    console.log(`  - CSS加载时间: ${report.cssLoadTime.toFixed(2)}ms`);

    console.log("💾 缓存性能:");
    console.log(`  - 缓存命中率: ${(report.cacheHitRate * 100).toFixed(1)}%`);
    console.log(`  - 缓存加载时间: ${report.cacheLoadTime.toFixed(2)}ms`);

    console.groupEnd();

    // 性能建议
    this.printPerformanceSuggestions(report);
  }

  /**
   * 打印性能建议
   */
  private printPerformanceSuggestions(report: PerformanceMetrics): void {
    const suggestions: string[] = [];

    if (report.firstContentfulPaint > 2000) {
      suggestions.push("首次内容绘制时间较长，建议优化关键资源加载");
    }

    if (report.largestContentfulPaint > 2500) {
      suggestions.push("最大内容绘制时间较长，建议优化图片和字体加载");
    }

    if (report.firstInputDelay > 100) {
      suggestions.push("首次输入延迟较长，建议减少主线程阻塞");
    }

    if (report.cumulativeLayoutShift > 0.1) {
      suggestions.push("累积布局偏移较大，建议为图片和广告预留空间");
    }

    if (report.cacheHitRate < 0.8) {
      suggestions.push("缓存命中率较低，建议优化缓存策略");
    }

    if (suggestions.length > 0) {
      console.group("💡 性能优化建议");
      suggestions.forEach(suggestion => console.log(`  - ${suggestion}`));
      console.groupEnd();
    }
  }

  /**
   * 销毁监控器
   */
  destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// 在开发模式下自动打印性能报告
if (import.meta.env.DEV) {
  window.addEventListener("load", () => {
    setTimeout(() => {
      performanceMonitor.printPerformanceReport();
    }, 3000); // 3秒后打印报告，确保所有指标都已收集
  });
}

export default PerformanceMonitor;
