/**
 * Service Worker for Visual Debug App
 * 提供激进的缓存策略以优化加载性能
 */

const CACHE_NAME = 'visualdebug-v1.1.0';
const STATIC_CACHE_NAME = 'visualdebug-static-v1.1.0';
const DYNAMIC_CACHE_NAME = 'visualdebug-dynamic-v1.1.0';

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/src/main.ts',
  '/src/App.vue',
  '/src/styles/var.scss',
  '/src/styles/reset.scss',
  '/src/styles/common.scss'
];

// 需要缓存的动态资源模式
const CACHE_PATTERNS = [
  /\.(?:js|css|woff2?|ttf|eot)$/,
  /\/assets\//,
  /\/src\//,
  /\/api\//
];

// 不需要缓存的资源模式
const NO_CACHE_PATTERNS = [
  /\/api\/auth\//,
  /\/api\/upload\//,
  /\.map$/,
  /hot-update/
];

// 安装事件
self.addEventListener('install', event => {
  console.log('SW: Installing...');
  
  event.waitUntil(
    Promise.all([
      // 缓存静态资源
      caches.open(STATIC_CACHE_NAME).then(cache => {
        console.log('SW: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      }),
      // 立即激活新的Service Worker
      self.skipWaiting()
    ])
  );
});

// 激活事件
self.addEventListener('activate', event => {
  console.log('SW: Activating...');
  
  event.waitUntil(
    Promise.all([
      // 清理旧缓存
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME && 
                cacheName !== STATIC_CACHE_NAME && 
                cacheName !== DYNAMIC_CACHE_NAME) {
              console.log('SW: Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // 立即控制所有客户端
      self.clients.claim()
    ])
  );
});

// 拦截请求
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // 跳过非GET请求
  if (request.method !== 'GET') {
    return;
  }
  
  // 跳过不需要缓存的资源
  if (NO_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname))) {
    return;
  }
  
  // 处理静态资源
  if (STATIC_ASSETS.includes(url.pathname) || url.pathname === '/') {
    event.respondWith(cacheFirst(request, STATIC_CACHE_NAME));
    return;
  }
  
  // 处理动态资源
  if (CACHE_PATTERNS.some(pattern => pattern.test(url.pathname))) {
    event.respondWith(staleWhileRevalidate(request, DYNAMIC_CACHE_NAME));
    return;
  }
  
  // 处理API请求
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(networkFirst(request, DYNAMIC_CACHE_NAME));
    return;
  }
  
  // 默认策略：网络优先
  event.respondWith(networkFirst(request, DYNAMIC_CACHE_NAME));
});

/**
 * 缓存优先策略
 * 适用于静态资源
 */
async function cacheFirst(request, cacheName) {
  try {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      console.log('SW: Cache hit:', request.url);
      return cachedResponse;
    }
    
    console.log('SW: Cache miss, fetching:', request.url);
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('SW: Cache first failed:', error);
    return new Response('Offline', { status: 503 });
  }
}

/**
 * 网络优先策略
 * 适用于API请求
 */
async function networkFirst(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('SW: Network failed, trying cache:', request.url);
    
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    return new Response('Offline', { status: 503 });
  }
}

/**
 * 陈旧内容重新验证策略
 * 适用于动态资源
 */
async function staleWhileRevalidate(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  // 后台更新缓存
  const fetchPromise = fetch(request).then(networkResponse => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  }).catch(error => {
    console.warn('SW: Background fetch failed:', error);
  });
  
  // 如果有缓存，立即返回缓存内容
  if (cachedResponse) {
    console.log('SW: Serving from cache:', request.url);
    return cachedResponse;
  }
  
  // 如果没有缓存，等待网络请求
  console.log('SW: No cache, waiting for network:', request.url);
  return fetchPromise;
}

// 消息处理
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CACHE_URLS') {
    const urls = event.data.urls;
    caches.open(DYNAMIC_CACHE_NAME).then(cache => {
      return cache.addAll(urls);
    });
  }
  
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
    }).then(() => {
      event.ports[0].postMessage({ success: true });
    });
  }
});

// 错误处理
self.addEventListener('error', event => {
  console.error('SW: Error:', event.error);
});

self.addEventListener('unhandledrejection', event => {
  console.error('SW: Unhandled rejection:', event.reason);
});

console.log('SW: Service Worker loaded');
