/**
 * 性能测试工具
 * 用于测试和验证性能优化效果
 */

import { performanceMonitor } from './performanceMonitor';
import { startupCacheManager } from './startupCache';

interface PerformanceTestResult {
  testName: string;
  duration: number;
  success: boolean;
  details?: any;
}

class PerformanceTest {
  private results: PerformanceTestResult[] = [];

  /**
   * 测试缓存性能
   */
  async testCachePerformance(): Promise<PerformanceTestResult> {
    const testName = '缓存性能测试';
    const startTime = performance.now();
    
    try {
      // 测试数据
      const testData = {
        routes: ['/', '/device', '/debug'],
        components: ['App', 'Header', 'Sidebar'],
        settings: { theme: 'dark', language: 'zh' }
      };
      
      // 写入缓存测试
      startupCacheManager.setCache('routes', testData.routes);
      startupCacheManager.setCache('components', testData.components);
      startupCacheManager.setCache('settings', testData.settings);
      
      // 读取缓存测试
      const cachedRoutes = startupCacheManager.getCache('routes');
      const cachedComponents = startupCacheManager.getCache('components');
      const cachedSettings = startupCacheManager.getCache('settings');
      
      // 验证缓存数据
      const isValid = 
        JSON.stringify(cachedRoutes) === JSON.stringify(testData.routes) &&
        JSON.stringify(cachedComponents) === JSON.stringify(testData.components) &&
        JSON.stringify(cachedSettings) === JSON.stringify(testData.settings);
      
      const duration = performance.now() - startTime;
      
      const result: PerformanceTestResult = {
        testName,
        duration,
        success: isValid,
        details: {
          cacheHits: 3,
          cacheMisses: 0,
          dataIntegrity: isValid
        }
      };
      
      this.results.push(result);
      return result;
      
    } catch (error) {
      const duration = performance.now() - startTime;
      const result: PerformanceTestResult = {
        testName,
        duration,
        success: false,
        details: { error: error.message }
      };
      
      this.results.push(result);
      return result;
    }
  }

  /**
   * 测试资源加载性能
   */
  async testResourceLoadPerformance(): Promise<PerformanceTestResult> {
    const testName = '资源加载性能测试';
    const startTime = performance.now();
    
    try {
      // 模拟资源加载
      const resources = [
        import('@/components/Common/BLRow.vue'),
        import('@/components/Common/BLCol.vue'),
        import('@/utils/iconify'),
        import('@/directives/index')
      ];
      
      await Promise.all(resources);
      
      const duration = performance.now() - startTime;
      
      const result: PerformanceTestResult = {
        testName,
        duration,
        success: true,
        details: {
          resourceCount: resources.length,
          averageLoadTime: duration / resources.length
        }
      };
      
      this.results.push(result);
      return result;
      
    } catch (error) {
      const duration = performance.now() - startTime;
      const result: PerformanceTestResult = {
        testName,
        duration,
        success: false,
        details: { error: error.message }
      };
      
      this.results.push(result);
      return result;
    }
  }

  /**
   * 测试内存使用情况
   */
  testMemoryUsage(): PerformanceTestResult {
    const testName = '内存使用测试';
    const startTime = performance.now();
    
    try {
      const memoryInfo = (performance as any).memory;
      
      if (!memoryInfo) {
        throw new Error('Memory API not supported');
      }
      
      const duration = performance.now() - startTime;
      
      const result: PerformanceTestResult = {
        testName,
        duration,
        success: true,
        details: {
          usedJSHeapSize: Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024 * 100) / 100,
          totalJSHeapSize: Math.round(memoryInfo.totalJSHeapSize / 1024 / 1024 * 100) / 100,
          jsHeapSizeLimit: Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024 * 100) / 100,
          unit: 'MB'
        }
      };
      
      this.results.push(result);
      return result;
      
    } catch (error) {
      const duration = performance.now() - startTime;
      const result: PerformanceTestResult = {
        testName,
        duration,
        success: false,
        details: { error: error.message }
      };
      
      this.results.push(result);
      return result;
    }
  }

  /**
   * 运行所有性能测试
   */
  async runAllTests(): Promise<PerformanceTestResult[]> {
    console.group('🧪 性能测试开始');
    
    const tests = [
      () => this.testCachePerformance(),
      () => this.testResourceLoadPerformance(),
      () => this.testMemoryUsage()
    ];
    
    for (const test of tests) {
      try {
        const result = await test();
        this.logTestResult(result);
      } catch (error) {
        console.error('测试执行失败:', error);
      }
    }
    
    this.printSummary();
    console.groupEnd();
    
    return this.results;
  }

  /**
   * 记录测试结果
   */
  private logTestResult(result: PerformanceTestResult): void {
    const status = result.success ? '✅' : '❌';
    const duration = result.duration.toFixed(2);
    
    console.log(`${status} ${result.testName}: ${duration}ms`);
    
    if (result.details) {
      console.log('   详细信息:', result.details);
    }
  }

  /**
   * 打印测试摘要
   */
  private printSummary(): void {
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
    
    console.group('📊 测试摘要');
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`总耗时: ${totalDuration.toFixed(2)}ms`);
    console.log(`平均耗时: ${(totalDuration / totalTests).toFixed(2)}ms`);
    console.groupEnd();
  }

  /**
   * 获取测试结果
   */
  getResults(): PerformanceTestResult[] {
    return [...this.results];
  }

  /**
   * 清除测试结果
   */
  clearResults(): void {
    this.results = [];
  }
}

// 创建全局性能测试实例
export const performanceTest = new PerformanceTest();

// 在开发模式下自动运行性能测试
if (import.meta.env.DEV) {
  // 延迟运行测试，确保应用完全加载
  setTimeout(() => {
    performanceTest.runAllTests();
  }, 5000);
}

export default PerformanceTest;
