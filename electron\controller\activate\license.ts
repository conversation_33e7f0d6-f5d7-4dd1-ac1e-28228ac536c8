"use strict";

import { licenseService } from "../../service/activate/license";
import { logger } from "ee-core/log";
import { ERROR_CODES, ERROR_MESSAGES, handleErrorResponse, handleCustomResponse } from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";

/**
 * License授权
 * <AUTHOR>
 * @class
 */
class LicenseController {
  /**
   * 获取机器码
   * getDeviceFile
   */
  async getMachineCode(): Promise<ApiResponse> {
    try {
      logger.info("[LicenseController] getMacheCode - Start");
      const machineCode = await licenseService.getMachineCode();
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        machineCode
      );
    } catch (error) {
      logger.error("[LicenseController] getMacheCode - Error occurred", error);
      return handleErrorResponse(error);
    }
  }

  /**
   * 检查授权
   * checkAuth
   */
  async checkAuth(): Promise<ApiResponse> {
    try {
      logger.info("[LicenseController] checkAuth - Start");
      const authResult = await licenseService.checkAuth();
      logger.info(`[LicenseController] checkAuth - Result: ${authResult}`);
      return handleCustomResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS, {
        authorized: authResult,
      });
    } catch (error) {
      logger.error("[LicenseController] checkAuth - Error occurred", error);
      return handleErrorResponse(error);
    }
  }

  /**
   * 清除授权缓存
   */
  async clearAuthCache(): Promise<ApiResponse> {
    try {
      logger.info("[LicenseController] clearAuthCache - Start");
      licenseService.clearAuthCache();
      return handleCustomResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS, {
        cleared: true,
      });
    } catch (error) {
      logger.error(
        "[LicenseController] clearAuthCache - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 授权激活
   * checkAuth
   */
  async activate(activeCode: string): Promise<ApiResponse> {
    try {
      logger.info("[LicenseController] activate - Start");
      const machineCode = await licenseService.activate(activeCode);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        machineCode
      );
    } catch (error) {
      logger.error("[LicenseController] activate - Error occurred", error);
      return handleErrorResponse(error);
    }
  }
}

LicenseController.toString = () => "[class LicenseController]";
export default LicenseController;
