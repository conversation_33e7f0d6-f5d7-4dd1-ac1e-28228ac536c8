# F12控制台功能增强说明

## 功能概述

本次更新为F12开发者工具添加了以下功能：

1. **默认中文界面** - 开发者工具将根据应用程序的语言设置自动显示对应语言
2. **复制粘贴功能** - 在开发者工具控制台中支持文本的复制和粘贴操作
3. **应用内控制台增强** - 为应用内的控制台组件添加了粘贴功能

## 功能详情

### 1. F12开发者工具语言设置

#### 自动语言检测
- 开发者工具会根据应用程序当前的语言设置自动配置界面语言
- 支持的语言：中文(zh-CN)、英文(en-US)、西班牙语(es-ES)、法语(fr-FR)
- 默认语言：中文

#### 语言映射
```typescript
const localeMap = {
  'zh': 'zh-CN',    // 中文
  'en': 'en-US',    // 英文
  'es': 'es-ES',    // 西班牙语
  'fr': 'fr-FR'     // 法语
};
```

### 2. 开发者工具复制粘贴功能

#### 键盘快捷键
- **Ctrl+C**: 复制选中的文本
- **Ctrl+V**: 在可编辑区域粘贴文本
- **Ctrl+A**: 全选文本

#### 右键菜单
- 选中文本后右键可显示"复制"选项
- 在可编辑区域右键可显示"粘贴"选项
- 菜单项为中文显示

#### 功能特点
- 支持控制台输出文本的复制
- 支持在控制台输入框中粘贴代码
- 自动处理剪贴板权限
- 提供错误处理和用户反馈

### 3. 应用内控制台增强

#### 新增功能
- **粘贴功能**: 右键菜单新增"粘贴"选项
- **改进的复制功能**: 如果没有选中文本，将复制全部内容
- **键盘快捷键支持**: Ctrl+C、Ctrl+V、Ctrl+A

#### 国际化支持
新增的文本已添加到所有支持的语言文件中：

**中文**:
- paste: "粘贴"
- copyAllSuccess: "复制全部内容成功"
- pasteSuccess: "粘贴成功"
- pasteFailed: "粘贴失败"
- noClipboardContent: "剪贴板中没有内容"

**英文**:
- paste: "Paste"
- copyAllSuccess: "Copy all content successful"
- pasteSuccess: "Paste successful"
- pasteFailed: "Paste failed"
- noClipboardContent: "No content in clipboard"

## 使用方法

### 打开F12开发者工具
1. 按下 **F12** 键
2. 开发者工具将以当前应用语言打开
3. 支持复制粘贴操作

### 在控制台中复制文本
1. 选中要复制的文本
2. 按 **Ctrl+C** 或右键选择"复制"
3. 文本将被复制到剪贴板

### 在控制台中粘贴文本
1. 在可编辑区域（如控制台输入框）点击
2. 按 **Ctrl+V** 或右键选择"粘贴"
3. 剪贴板内容将被粘贴到当前位置

### 应用内控制台操作
1. 在设备调试页面的控制台区域
2. 右键可看到"复制"、"粘贴"、"全选"、"清空"选项
3. 支持键盘快捷键操作

## 技术实现

### 开发者工具语言设置
本次更新采用了多层次的语言设置策略，确保开发者工具能够显示中文：

#### 1. 应用启动时设置
- 在主进程启动时设置环境变量（LANGUAGE、LC_ALL、LANG）
- 通过Chrome命令行参数设置语言（--lang、--locale）
- 在窗口创建时预设置语言偏好

#### 2. 开发者工具打开时设置
- 立即执行语言设置脚本
- 设置localStorage和sessionStorage中的语言配置
- 修改navigator.language属性
- 设置HTML文档的lang属性

#### 3. 多重保障机制
- 通过多种API设置语言（InspectorFrontendHost、DevToolsAPI、Common.settings）
- 多次尝试设置（在不同时间点重复设置）
- 自动刷新开发者工具界面以应用语言设置

#### 4. 强制设置方法
```javascript
// 设置所有可能的语言配置项
const configs = [
  ['language', 'zh-CN'],
  ['locale', 'zh-CN'],
  ['currentLocale', 'zh-CN'],
  ['devtools-language', 'zh-CN'],
  ['InspectorFrontendHost.language', 'zh-CN']
];
```

### 复制粘贴功能实现
- 使用 `navigator.clipboard` API
- 自定义右键菜单
- 键盘事件监听和处理
- 错误处理和用户反馈

### 国际化支持
- 所有新增文本都已添加到语言文件
- 支持动态语言切换
- 保持与应用整体语言设置的一致性

## 注意事项

1. **浏览器兼容性**: 复制粘贴功能需要现代浏览器支持
2. **权限要求**: 某些操作可能需要用户授权剪贴板访问权限
3. **安全限制**: 在某些安全上下文中，剪贴板操作可能受限

## 故障排除

### 开发者工具语言未切换

#### 基本排查步骤
1. **检查应用程序语言设置**
   - 确认应用程序当前语言为中文
   - 在应用程序中切换到中文语言

2. **重启应用程序**
   - 完全关闭应用程序
   - 重新启动应用程序
   - 再次按F12打开开发者工具

3. **强制刷新开发者工具**
   - 在开发者工具中按F5刷新
   - 或者关闭开发者工具后重新打开

#### 高级排查方法
1. **检查环境变量**
   - 在开发者工具控制台中输入：`console.log(navigator.language)`
   - 应该显示 'zh-CN'

2. **检查localStorage设置**
   ```javascript
   // 在开发者工具控制台中执行
   console.log('language:', localStorage.getItem('language'));
   console.log('locale:', localStorage.getItem('locale'));
   ```

3. **手动设置语言**
   ```javascript
   // 在开发者工具控制台中执行
   localStorage.setItem('language', 'zh-CN');
   localStorage.setItem('locale', 'zh-CN');
   location.reload();
   ```

#### 如果仍然无效
- 清除浏览器缓存和开发者工具设置
- 检查Chrome浏览器的系统语言设置
- 查看应用程序启动日志中的语言设置信息

### 复制粘贴功能不工作
- 确认浏览器支持剪贴板API
- 检查是否有权限限制
- 查看控制台错误信息

### 应用内控制台问题
- 确认控制台组件已正确加载
- 检查国际化文件是否正确更新
- 验证事件监听器是否正常工作
