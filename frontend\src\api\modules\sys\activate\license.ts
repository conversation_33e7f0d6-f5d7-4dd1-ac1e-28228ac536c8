import { moduleIpcRequest } from "@/api/request";
const ipc = moduleIpcRequest("controller/activate/license/");

const licenseApi = {
  // 获取机器码
  getMachineCode() {
    console.log("licenseApi.getMachineCode");
    return ipc.invoke<{}>("getMachineCode");
  },
  // 检查授权
  checkAuth() {
    console.log("licenseApi.checkAuth");
    return ipc.invoke<{}>("checkAuth");
  },
  // 清除授权缓存
  clearAuthCache() {
    console.log("licenseApi.clearAuthCache");
    return ipc.invoke<{}>("clearAuthCache");
  },
  // 授权激活
  activate(activeCode: any) {
    console.log("licenseApi.activate");
    return ipc.invoke<{}>("activate", activeCode);
  }
};

export { licenseApi };
