import { app, powerMonitor, BrowserWindow } from "electron";
import { getMainWindow } from "ee-core/electron";
import { logger } from "ee-core/log";
import { windowStateService } from "./windowState";
import { t } from "../../data/i18n/i18n";

/**
 * 系统事件监听服务
 * 监听各种系统事件，确保应用状态的正确保存和恢复
 */
class SystemEventsService {
  private isInitialized = false;
  private suspendTimer: NodeJS.Timeout | null = null;
  private resumeTimer: NodeJS.Timeout | null = null;

  /**
   * 初始化系统事件监听
   */
  public initialize(): void {
    if (this.isInitialized) return;

    logger.info(
      `[SystemEventsService] ${t("services.systemEvents.systemEventsInitialized")}`
    );

    // 等待应用准备就绪
    if (app.isReady()) {
      this.setupEventListeners();
    } else {
      app.whenReady().then(() => {
        this.setupEventListeners();
      });
    }

    this.isInitialized = true;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    try {
      // 监听系统休眠事件
      powerMonitor.on("suspend", this.handleSuspend.bind(this));

      // 监听系统唤醒事件
      powerMonitor.on("resume", this.handleResume.bind(this));

      // 监听锁屏事件
      powerMonitor.on("lock-screen", this.handleLockScreen.bind(this));

      // 监听解锁事件
      powerMonitor.on("unlock-screen", this.handleUnlockScreen.bind(this));

      // 监听系统关机事件
      powerMonitor.on("shutdown", this.handleShutdown.bind(this));

      // 监听应用即将退出事件
      app.on("before-quit", this.handleBeforeQuit.bind(this));

      // 监听所有窗口关闭事件
      app.on("window-all-closed", this.handleWindowAllClosed.bind(this));

      // 监听应用激活事件（macOS）
      app.on("activate", this.handleActivate.bind(this));

      // 监听应用失去焦点事件
      app.on("browser-window-blur", this.handleWindowBlur.bind(this));

      // 监听应用获得焦点事件
      app.on("browser-window-focus", this.handleWindowFocus.bind(this));

      logger.info(
        `[SystemEventsService] ${t("services.systemEvents.eventListenersSet")}`
      );
    } catch (error) {
      logger.error(
        `[SystemEventsService] ${t("services.systemEvents.setupEventListenersFailed")}:`,
        error
      );
    }
  }

  /**
   * 处理系统休眠事件
   */
  private handleSuspend(): void {
    logger.info(
      `[SystemEventsService] ${t("services.systemEvents.systemSuspending")}`
    );

    // 清除之前的定时器
    if (this.suspendTimer) {
      clearTimeout(this.suspendTimer);
    }

    // 设置窗口状态为suspended
    windowStateService.setAppStatus("suspended");

    // 立即保存状态
    this.saveApplicationState();

    // 设置一个短暂的延迟，确保状态保存完成
    this.suspendTimer = setTimeout(() => {
      logger.debug(
        `[SystemEventsService] ${t("services.systemEvents.applicationStateSaved")}`
      );
    }, 100);
  }

  /**
   * 处理系统唤醒事件
   */
  private handleResume(): void {
    logger.info(
      `[SystemEventsService] ${t("services.systemEvents.systemResuming")}`
    );

    // 清除之前的定时器
    if (this.resumeTimer) {
      clearTimeout(this.resumeTimer);
    }

    // 设置窗口状态为running
    windowStateService.setAppStatus("running");

    // 延迟恢复状态，确保系统完全唤醒
    this.resumeTimer = setTimeout(() => {
      this.restoreApplicationState();
      logger.debug(
        `[SystemEventsService] ${t("services.systemEvents.applicationStateRestored")}`
      );
    }, 500);
  }

  /**
   * 处理锁屏事件
   */
  private handleLockScreen(): void {
    logger.info(
      `[SystemEventsService] ${t("services.systemEvents.screenLocked")}`
    );
    windowStateService.setAppStatus("suspended");
  }

  /**
   * 处理解锁事件
   */
  private handleUnlockScreen(): void {
    logger.info(
      `[SystemEventsService] ${t("services.systemEvents.screenUnlocked")}`
    );
    windowStateService.setAppStatus("running");
    this.restoreApplicationState();
  }

  /**
   * 处理系统关机事件
   */
  private handleShutdown(): void {
    logger.info(
      `[SystemEventsService] ${t("services.systemEvents.systemShuttingDown")}`
    );
    this.saveApplicationState();
  }

  /**
   * 处理应用即将退出事件
   */
  private handleBeforeQuit(): void {
    logger.info(
      `[SystemEventsService] ${t("services.systemEvents.appBeforeQuit")}`
    );
    this.saveApplicationState();
  }

  /**
   * 处理所有窗口关闭事件
   */
  private handleWindowAllClosed(): void {
    logger.info(
      `[SystemEventsService] ${t("services.systemEvents.allWindowsClosed")}`
    );
    this.saveApplicationState();
  }

  /**
   * 处理应用激活事件（macOS）
   */
  private handleActivate(): void {
    logger.info(
      `[SystemEventsService] ${t("services.systemEvents.appActivated")}`
    );

    // 在macOS上，当应用被激活时，如果没有窗口打开，创建一个新窗口
    if (process.platform === "darwin") {
      const mainWindow = getMainWindow();
      if (mainWindow && !mainWindow.isVisible()) {
        mainWindow.show();
      }
    }

    this.restoreApplicationState();
  }

  /**
   * 处理窗口失去焦点事件
   */
  private handleWindowBlur(event: any, window: BrowserWindow): void {
    logger.debug(
      `[SystemEventsService] ${t("services.systemEvents.windowBlurred")}`
    );
    // 可以在这里添加窗口失去焦点时的处理逻辑
  }

  /**
   * 处理窗口获得焦点事件
   */
  private handleWindowFocus(event: any, window: BrowserWindow): void {
    logger.debug(
      `[SystemEventsService] ${t("services.systemEvents.windowFocused")}`
    );
    // 可以在这里添加窗口获得焦点时的处理逻辑
  }

  /**
   * 保存应用状态
   */
  private saveApplicationState(): void {
    try {
      // 保存窗口状态
      windowStateService.saveWindowState();

      // 可以在这里添加其他状态保存逻辑
      // 例如：保存用户偏好、缓存数据等

      logger.debug(
        `[SystemEventsService] ${t("services.systemEvents.applicationStateSaved")}`
      );
    } catch (error) {
      logger.error(
        `[SystemEventsService] ${t("services.systemEvents.applicationStateSaved")} error:`,
        error
      );
    }
  }

  /**
   * 恢复应用状态
   */
  private restoreApplicationState(): void {
    try {
      // 恢复窗口状态
      windowStateService.restoreWindowState();

      // 可以在这里添加其他状态恢复逻辑
      // 例如：恢复用户偏好、重新连接网络等

      logger.debug(
        `[SystemEventsService] ${t("services.systemEvents.applicationStateRestored")}`
      );
    } catch (error) {
      logger.error(
        `[SystemEventsService] ${t("services.systemEvents.applicationStateRestored")} error:`,
        error
      );
    }
  }

  /**
   * 获取电源信息
   */
  public getPowerInfo(): any {
    try {
      const powerInfo = {
        onBattery: powerMonitor.isOnBatteryPower(),
        onBatteryPower: powerMonitor.isOnBatteryPower(),
        idleTime: powerMonitor.getSystemIdleTime(),
        idleState: powerMonitor.getSystemIdleState(1), // 1秒空闲状态
      };

      logger.debug(
        `[SystemEventsService] ${t("services.systemEvents.powerInfoRetrieved")}:`,
        powerInfo
      );
      return powerInfo;
    } catch (error) {
      logger.error(
        `[SystemEventsService] ${t("services.systemEvents.powerInfoRetrieved")} error:`,
        error
      );
      return null;
    }
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    try {
      // 清除定时器
      if (this.suspendTimer) {
        clearTimeout(this.suspendTimer);
        this.suspendTimer = null;
      }

      if (this.resumeTimer) {
        clearTimeout(this.resumeTimer);
        this.resumeTimer = null;
      }

      // 保存最终状态
      this.saveApplicationState();

      logger.info(
        `[SystemEventsService] ${t("services.systemEvents.cleanupCompleted")}`
      );
    } catch (error) {
      logger.error(
        `[SystemEventsService] ${t("services.systemEvents.cleanupCompleted")} error:`,
        error
      );
    }
  }
}

// 创建单例实例
const systemEventsService = new SystemEventsService();

export { SystemEventsService, systemEventsService };
