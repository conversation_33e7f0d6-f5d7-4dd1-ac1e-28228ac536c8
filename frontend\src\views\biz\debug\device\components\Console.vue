<template>
  <div class="console-area">
    <bl-row just="space-between" height="28px" class="console-area-workbench" :style="consoleMainStyle.consoleWorkbench">
      <div style="display: flex; align-items: center">
        <span>{{ t("device.console.title") }}</span>
      </div>
      <div style="display: flex; align-items: center">
        <el-tooltip :content="t('device.console.clear')" placement="bottom">
          <i class="iconfont toolBar-icon" @click="clear">
            <svg-icon icon="ant-design:delete-outlined" style="font-size: 20px; color: var(--el-color-info)"></svg-icon>
          </i>
        </el-tooltip>
        <el-tooltip :content="contentDesc" placement="bottom">
          <i class="iconfont toolBar-icon" @click="hideConsole">
            <svg-icon icon="eva:minus-outline" style="font-size: 20px; color: var(--el-color-info)"></svg-icon>
          </i>
        </el-tooltip>
      </div>
    </bl-row>
    <bl-row class="console-area-input" :style="consoleMainStyle.consoleArea">
      <el-input
        ref="consoleInput"
        v-model="formatConsole"
        v-contextmenu:contextmenu
        type="textarea"
        resize="none"
        :readonly="true"
        @keydown="handleKeyDown"
      ></el-input>
    </bl-row>

    <!-- 拖拽调整高度的手柄 -->
    <div v-if="globalStore.isConsole" class="console-resize-handle" @mousedown="startResize" @touchstart="startResize"></div>

    <v-contextmenu ref="contextmenu">
      <v-contextmenu-item @click="selectAll">
        <svg-icon icon="ant-design:select-outlined" style="margin-right: 8px" />
        {{ t("device.console.selectAll") }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="copy">
        <svg-icon icon="ant-design:copy-outlined" style="margin-right: 8px" />
        {{ t("device.console.copy") }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="paste">
        <svg-icon icon="ant-design:snippets-outlined" style="margin-right: 8px" />
        {{ t("device.console.paste") }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="clear">
        <svg-icon icon="ant-design:delete-outlined" style="margin-right: 8px" />
        {{ t("device.console.clear") }}
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>
<script setup lang="ts">
import Message from "@/scripts/message";
import { createScopeDebugStore, useDebugStore } from "@/stores/modules/debug";
import { isEmpty, join } from "lodash";
import { useGlobalStore } from "@/stores/modules";
import { ContextmenuInstance } from "v-contextmenu/es/types";
import { ElInput } from "element-plus";
import { useI18n } from "vue-i18n";
const props = defineProps({ scopeid: String });
let debugStore;
if (props.scopeid) {
  debugStore = createScopeDebugStore(props.scopeid)();
} else {
  debugStore = useDebugStore();
}
const { consoleLog, clearConsole } = debugStore;
const globalStore = useGlobalStore();
const contextmenu = ref<ContextmenuInstance>();
const consoleInput = ref<InstanceType<typeof ElInput>>();
const { t } = useI18n();
const formatConsole = computed(() => {
  if (isEmpty(consoleLog)) {
    return "";
  }
  return join(consoleLog, "\n");
});

const consoleMainStyle = computed<any>(() => {
  if (globalStore.isConsole) {
    return { consoleArea: { height: `${globalStore.consoleHeight}px` }, consoleWorkbench: {} };
  }
  return { consoleArea: { height: "0px", padding: "" }, consoleWorkbench: { borderBottom: "0px" } };
});

// 拖拽调整高度相关变量
let isResizing = false;
let startY = 0;
let startHeight = 0;

// 开始拖拽调整高度
const startResize = (event: MouseEvent | TouchEvent) => {
  event.preventDefault();
  isResizing = true;

  if (event instanceof MouseEvent) {
    startY = event.clientY;
  } else if (event instanceof TouchEvent) {
    startY = event.touches[0].clientY;
  }

  startHeight = globalStore.consoleHeight;

  document.addEventListener("mousemove", doResize);
  document.addEventListener("mouseup", stopResize);
  document.addEventListener("touchmove", doResize);
  document.addEventListener("touchend", stopResize);

  // 添加拖拽时的样式
  document.body.style.cursor = "ns-resize";
  document.body.style.userSelect = "none";
};

// 执行拖拽调整
const doResize = (event: MouseEvent | TouchEvent) => {
  if (!isResizing) return;

  let currentY = 0;
  if (event instanceof MouseEvent) {
    currentY = event.clientY;
  } else if (event instanceof TouchEvent) {
    currentY = event.touches[0].clientY;
  }

  const deltaY = startY - currentY;
  const newHeight = Math.max(100, Math.min(500, startHeight + deltaY)); // 限制最小100px，最大500px

  globalStore.consoleHeight = newHeight;
};

// 停止拖拽调整
const stopResize = () => {
  isResizing = false;

  document.removeEventListener("mousemove", doResize);
  document.removeEventListener("mouseup", stopResize);
  document.removeEventListener("touchmove", doResize);
  document.removeEventListener("touchend", stopResize);

  // 恢复样式
  document.body.style.cursor = "";
  document.body.style.userSelect = "";
};

// 组件卸载时清理事件监听器
onUnmounted(() => {
  document.removeEventListener("mousemove", doResize);
  document.removeEventListener("mouseup", stopResize);
  document.removeEventListener("touchmove", doResize);
  document.removeEventListener("touchend", stopResize);
});

const selectAll = () => {
  consoleInput.value?.textarea?.select();
};

const handleKeyDown = (event: KeyboardEvent) => {
  // Ctrl+C 复制
  if (event.ctrlKey && event.key === "c") {
    event.preventDefault();
    copy();
  }
  // Ctrl+V 粘贴
  else if (event.ctrlKey && event.key === "v") {
    event.preventDefault();
    paste();
  }
  // Ctrl+A 全选
  else if (event.ctrlKey && event.key === "a") {
    event.preventDefault();
    selectAll();
  }
};

const copy = async () => {
  try {
    const textarea = consoleInput.value?.textarea;
    if (textarea) {
      const selectedText = textarea.value.substring(textarea.selectionStart, textarea.selectionEnd);
      if (selectedText) {
        await navigator.clipboard.writeText(selectedText);
        Message.success(t("device.console.copySuccess"));
      } else {
        // 如果没有选中文本，复制全部内容
        await navigator.clipboard.writeText(textarea.value);
        Message.success(t("device.console.copyAllSuccess"));
      }
    }
  } catch (err) {
    Message.error(t("device.console.copyFailed"));
  }
};

const paste = async () => {
  try {
    const clipboardText = await navigator.clipboard.readText();
    if (clipboardText) {
      // 将粘贴的文本添加到控制台日志中
      const debugStore = useDebugStore();
      debugStore.addConsoleLog(clipboardText);
      Message.success(t("device.console.pasteSuccess"));
    } else {
      Message.warning(t("device.console.noClipboardContent"));
    }
  } catch (err) {
    Message.error(t("device.console.pasteFailed"));
  }
};
const clear = () => {
  clearConsole();
  Message.success(t("device.console.clearSuccess"));
};
const hideConsole = () => {
  globalStore.isConsole = !globalStore.isConsole;
};

const contentDesc = computed(() => {
  if (globalStore.isConsole) {
    return t("device.console.collapse");
  }
  return t("device.console.expand");
});

watch(
  () => consoleLog,
  () => {
    nextTick(() => {
      const textarea = consoleInput.value?.textarea;
      if (textarea) {
        textarea.scrollTop = textarea.scrollHeight;
      }
    });
  },
  {
    deep: true
  }
);
</script>
<style scoped lang="scss">
.console-area {
  position: relative;
  width: auto;
  border: 1px solid var(--el-border-color);
  .console-area-workbench {
    color: var(--el-text-color);
    background-color: var(--el-color-primary-light-9);
    border-bottom: 1px solid var(--el-border-color);
    div:first-child {
      margin-left: 10px;
      font-size: 15px;
      font-weight: bold;
      span {
        margin-bottom: 1px;
      }
    }
    i {
      margin-right: 10px;
    }
  }
  .console-area-input {
    overflow: hidden;
    :deep(.el-textarea__inner) {
      height: v-bind('globalStore.consoleHeight + "px"');
      box-shadow: none;
    }
  }
  .console-resize-handle {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 10;
    height: 4px;
    cursor: ns-resize;
    background: transparent;
    &:hover {
      background: rgb(64 158 255 / 10%);
    }
    &::after {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 30px;
      height: 2px;
      content: "";
      background: var(--el-border-color);
      border-radius: 1px;
      transform: translate(-50%, -50%);
    }
  }
}
:deep(.v-contextmenu-item) {
  display: flex;
  align-items: center;
  .svg-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
    margin-right: 8px;
    font-size: 18px;
    color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    border-radius: 50%;
    transition: color 0.2s;
  }
  &:hover {
    color: var(--el-color-primary);
    background: var(--el-color-primary-light-8);
    .svg-icon {
      color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
    }
  }
}
</style>
