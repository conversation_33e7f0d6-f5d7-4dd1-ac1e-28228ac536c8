import { defineStore } from "pinia";
import piniaPersistConfig from "@/stores/helper/persist";

// 定义激活状态的接口
export interface ActivateState {
  isActivated: boolean;
}

const name = "simple-activate"; // 定义模块名称

// 创建和导出激活状态的store
export const useActivateStore = defineStore({
  id: name,
  state: (): ActivateState => ({
    isActivated: false
  }),
  getters: {
    // 获取激活状态
    getActivated(): boolean {
      console.log(`[ActivateStore] 获取激活状态: ${this.isActivated}`);
      return this.isActivated;
    }
  },
  actions: {
    // 设置激活状态
    setActivated(isActivated: boolean) {
      console.log(`[ActivateStore] 设置激活状态: ${this.isActivated} -> ${isActivated}`);
      this.isActivated = isActivated;
      console.log(`[ActivateStore] 激活状态已设置为: ${this.isActivated}`);
    }
  },
  persist: piniaPersistConfig(name, ["isActivated"])
});
