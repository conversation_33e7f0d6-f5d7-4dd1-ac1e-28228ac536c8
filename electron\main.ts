import { ElectronEgg } from "ee-core";
import { Lifecycle } from "./preload/lifecycle";
import { preload } from "./preload";
import { app, BrowserWindow } from "electron";
import { initI18n, getCurrentLanguage } from "./data/i18n/i18n";
import { systemEventsService } from "./service/os/systemEvents";

// Initialize i18n system
initI18n();

// 设置开发者工具的默认语言环境
const currentLang = getCurrentLanguage();
const devToolsLocale =
  currentLang === "zh"
    ? "zh-CN"
    : currentLang === "en"
      ? "en-US"
      : currentLang === "es"
        ? "es-ES"
        : currentLang === "fr"
          ? "fr-FR"
          : "zh-CN";

// 设置环境变量强制开发者工具使用指定语言
process.env.LANGUAGE = devToolsLocale;
process.env.LC_ALL = devToolsLocale;
process.env.LANG = devToolsLocale;

// 设置Chrome的语言参数
app.commandLine.appendSwitch("lang", devToolsLocale);
app.commandLine.appendSwitch("locale", devToolsLocale);

// New app
const electronApp = new ElectronEgg();

// Set Content Security Policy
app.on("web-contents-created", (event, contents) => {
  contents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        "Content-Security-Policy": [
          "default-src 'self';",
          "script-src 'self' 'unsafe-inline';",
          "style-src 'self' 'unsafe-inline';",
          "img-src 'self' data: https:;",
          "connect-src 'self' https:;",
          "font-src 'self' data:;",
          "object-src 'none';",
          "base-uri 'self';",
          "form-action 'self';",
          "frame-ancestors 'none';",
          "block-all-mixed-content;",
          "upgrade-insecure-requests;",
        ].join(" "),
      },
    });
  });
});

// Register lifecycle
const life = new Lifecycle();
electronApp.register("ready", life.ready);
electronApp.register("electron-app-ready", () => {
  life.electronAppReady();
  // Initialize system events service
  systemEventsService.initialize();
});
electronApp.register("window-ready", life.windowReady);
electronApp.register("before-close", life.beforeClose);
// Register preload
electronApp.register("preload", preload);

// Run
electronApp.run();
