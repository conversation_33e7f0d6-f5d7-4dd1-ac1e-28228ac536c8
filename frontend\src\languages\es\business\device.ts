export default {
  console: {
    title: "<PERSON>sol<PERSON>",
    clear: "Limpiar",
    selectAll: "Seleccionar todo",
    copy: "Copiar",
    paste: "Pegar",
    copySuccess: "Copiado con éxito",
    copyAllSuccess: "Todo el contenido copiado con éxito",
    noTextSelected: "No hay texto seleccionado",
    copyFailed: "Error al copiar",
    pasteSuccess: "Pegado con éxito",
    pasteFailed: "Error al pegar",
    noClipboardContent: "No hay contenido en el portapapeles",
    clearSuccess: "Consola limpiada",
    collapse: "Colapsar",
    expand: "Expandir"
  },
  groupInfo: {
    title: "Información de Grupo",
    table: {
      id: "Índice",
      name: "Nombre",
      desc: "Descripción",
      fc: "FC",
      count: "Cantidad"
    },
    messages: {
      fetchDataError: "Error al obtener datos",
      fetchedData: "Datos obtenidos:"
    }
  },
  treeClickLog: "Clic en treeClick : ",
  contentView: "Vista de contenido",
  emptyDeviceId: "El id del dispositivo actual está vacío",
  invalidResponseStructure: "Estructura de respuesta inválida",
  formattedMenuDataLog: "Datos de menú formateados ===",
  allSettings: "Todos los valores",
  allEditSpSettings: "Todos los valores de zona única",
  allEditSgSettings: "Todos los valores de zona múltiple",
  deviceTreeDataLog: "Datos del árbol de dispositivos",
  failedToLoadMenu: "Error al cargar el menú del dispositivo:",
  innerTabs: {
    contentView: "Contenido",
    fileUpload: "Subir",
    fileDownload: "Descargar",
    deviceTime: "Sincronizar",
    deviceOperate: "Operación",
    variableDebug: "Debug",
    oneClickBackup: "Backup",
    entryConfig: "Config",
    tabClickLog: "Clic en pestaña:"
  },
  devices: {
    notConnectedAlt: "Dispositivo no conectado",
    pleaseConnect: "¡Por favor conecte el dispositivo primero!"
  },
  list: {
    unnamedDevice: "Dispositivo sin nombre",
    connected: "Conectado",
    disconnected: "Desconectado",
    connect: "Conectado",
    edit: "Editar",
    disconnect: "Desconectado",
    remove: "Eliminar",
    noDeviceFound: "No se encontró el dispositivo",
    handleClickLog: "Clic en handleListClick:",
    disconnectBeforeEdit: "Por favor, primero desconéctese para editar",
    connectSuccess: "Dispositivo {name}: Conexión exitosa",
    connectExist: "Dispositivo {name}: Conexión ya existe",
    connectFailed: "Dispositivo {name}: Conexión fallida",
    connectFailedReason: "Razón de falla de conexión del dispositivo:",
    disconnectedSuccess: "Dispositivo {name}: Desconectado",
    operateFailed: "Dispositivo {name}: Operación fallida",
    disconnectBeforeDelete: "Por favor, primero desconéctese para eliminar",
    dataLog: "Datos:",
    ipPortExist: "El IP y el puerto ya existen, no repita la adición"
  },
  search: {
    placeholder: "Buscar dispositivo",
    ipPortExist: "El IP y el puerto ya existen, no repita la adición"
  },
  summaryPie: {
    other: "Otro",
    title: "Porcentaje de valores",
    subtext: "Valores de grupo de valores"
  },
  deviceInfo: {
    title: "Información del dispositivo",
    export: "Exportar",
    exportTitle: "Exportar información del dispositivo",
    exportLoading: "Exportando información básica del dispositivo...",
    exportSuccess: "Exportación de información básica del dispositivo exitosa",
    exportFailed: "Exportación de información básica del dispositivo fallida",
    getInfoFailed: "Error al obtener información del dispositivo. Razón: {msg}",
    getInfoFailedEmpty: "Error al obtener información del dispositivo. Razón: Datos vacíos!",
    defaultFileName: "Información del dispositivo.xlsx",
    confirm: "Confirmar",
    tip: "Sugerencia"
  },
  allParamSetting: {
    title: "Todos los valores",
    autoRefresh: "Actualización automática",
    refresh: "Actualizar",
    confirm: "Confirmar",
    import: "Importar",
    export: "Exportar",
    groupTitle: "Grupo de valores:",
    allGroups: "Todos",
    noDataToImport: "No hay datos para importar",
    importSuccess: "Importación de valores exitosa",
    importFailed: "Importación de valores fallida: {msg}",
    requestFailed: "Solicitud fallida, por favor intente más tarde",
    queryFailed: "Consulta de valores fallida: {msg}",
    unsavedChanges: "Existen cambios no guardados, ¿desea continuar actualizando?",
    confirmButton: "Confirmar",
    cancelButton: "Cancelar",
    alertTitle: "Sugerencia",
    errorTitle: "Error",
    noDataToConfirm: "No hay datos para confirmar",
    confirmSuccess: "Actualización de valores exitosa",
    confirmFailed: "Actualización de valores fallida: ",
    responseLog: "Datos de respuesta:",
    continueAutoRefresh: "Continuar actualización automática",
    settingGroup: "Grupo de valores:",
    all: "Todos",
    minValue: "Valor mínimo",
    maxValue: "Valor máximo",
    step: "Paso",
    unit: "Unidad",
    searchNamePlaceholder: "Ingrese el nombre del valor para buscar",
    searchDescPlaceholder: "Ingrese la descripción del valor para buscar",
    autoRefreshWarning: "No se permite modificar datos cuando la actualización automática está habilitada",
    invalidValue: "El valor {name} del valor {value} no está en el rango válido",
    exportFileName: "Parámetros de valores del dispositivo_Todos los valores.xlsx",
    selectPathLog: "Seleccionar ruta: ",
    exportSuccess: "Exportar lista de valores exitosa"
  },
  variable: {
    autoRefresh: "Actualización automática",
    variableName: "Nombre de variable",
    inputVariableName: "Ingrese el nombre de la variable para agregar",
    refresh: "Actualizar",
    add: "Agregar",
    confirm: "Confirmar",
    import: "Importar",
    export: "Exportar",
    delete: "Eliminar",
    noDataToConfirm: "No hay datos para confirmar",
    warning: "Alerta",
    variableModifiedSuccess: "Modificación de variable exitosa",
    variableModifiedFailed: "Modificación de variable fallida, razón:",
    requestFailed: "Solicitud fallida, por favor intente más tarde",
    error: "Error",
    success: "Éxito",
    variableAddSuccess: "Variable de dispositivo de depuración agregada exitosamente",
    variableAddFailed: "Variable de dispositivo de depuración agregada fallida, razón:",
    variableDeleteSuccess: "Variable de dispositivo de depuración eliminada exitosamente",
    variableDeleteFailed: "Variable de dispositivo de depuración eliminada fallida, razón:",
    exportSuccess: "Exportación de información de depuración de dispositivo exitosa",
    exportFailed: "Exportación de información de depuración de dispositivo fallida, razón:",
    importSuccess: "Importación de información de depuración de dispositivo exitosa",
    importFailed: "Importación de información de depuración de dispositivo fallida:",
    confirmRefresh: "Existen cambios no guardados, ¿desea continuar actualizando?",
    confirmAutoRefresh: "Existen cambios no guardados, ¿desea continuar actualizando automáticamente?",
    pleaseInputVariableName: "Por favor, ingrese el nombre de la variable",
    exportTitle: "Exportar información de depuración de dispositivo",
    importTitle: "Importar información de depuración de dispositivo",
    defaultExportPath: "Información de depuración de dispositivo.xlsx",
    title: "Depuración de variable",
    variableNamePlaceholder: "Ingrese el nombre de la variable para agregar",
    batchDelete: "Eliminar por lotes",
    modifySuccess: "Modificación de variable exitosa",
    modifyFailed: "Modificación de variable fallida, razón: {msg}",
    alertTitle: "Alerta",
    successTitle: "Sugerencia",
    errorTitle: "Error",
    confirmButton: "Confirmar",
    cancelButton: "Cancelar",
    sequence: "Índice",
    name: "Nombre",
    value: "Valor",
    type: "Tipo",
    description: "Descripción",
    address: "Dirección",
    operation: "Operación",
    enterVariableName: "Ingrese el nombre de la variable para agregar",
    responseLog: "Datos de respuesta:",
    addSuccess: "Variable de dispositivo de depuración agregada exitosamente",
    addFailed: "Variable de dispositivo de depuración agregada fallida, razón:",
    addFailedWithName: "Variable {name} agregada fallida: {reason}",
    exportFileName: "Información de depuración de dispositivo.xlsx",
    selectPathLog: "Seleccionar ruta:",
    exportSuccessLog: "Exportación de información de depuración de dispositivo exitosa, {path}",
    exportFailedLog: "Exportación de información de depuración de dispositivo fallida, razón:",
    importFailedLog: "Importación de información de depuración de dispositivo fallida:",
    unsavedChanges: "Existen cambios no guardados, ¿desea continuar actualizando?",
    continueAutoRefresh: "Continuar actualización automática",
    tip: "Sugerencia",
    sequenceNumber: "Índice"
  },
  backup: {
    sequence: "Índice",
    title: "Copia de seguridad del dispositivo",
    savePath: "Ruta de guardado",
    setPath: "Establecer ruta de guardado",
    setPathTitle: "Establecer ruta",
    startBackup: "Iniciar copia de seguridad",
    cancelBackup: "Cancelar copia de seguridad",
    backup: "Copia de seguridad",
    backupType: "Tipo de copia de seguridad",
    progress: "Progreso",
    status: "Estado",
    operation: "Operación",
    backupTypes: {
      paramValue: "Valores de parámetros del dispositivo",
      faultInfo: "Informes de fallos del dispositivo",
      cidConfigPrjLog: "CID/CCD/Configuración del dispositivo/Información de depuración/PRJ/Registro",
      waveReport: "Archivos de onda del dispositivo"
    },
    backupDesc: "Descripción del contenido de la copia de seguridad",
    backupDescTypes: {
      paramValue: "Exportar valores de parámetros del dispositivo (param export.xlsx)",
      faultInfo: "Exportar información de fallos del dispositivo (informes de evento/operación/fallo/auditoría)",
      cidConfigPrjLog: "Exportar archivos de configuración (CID/CCD, configuración XML, archivos de registro)",
      waveReport: "Exportar archivos de onda del dispositivo (/wave/comtrade)"
    },
    locateFolder: "Ubicar carpeta",
    backupSuccess: "Copia de seguridad exitosa",
    openFolderFailed: "Error al abrir la carpeta",
    backupFailed: "Copia de seguridad fallida",
    noTypeSelected: "Por favor, seleccione primero el tipo de copia de seguridad",
    cancelSuccess: "Cancelación exitosa",
    cancelFailed: "Error al cancelar"
  },
  operate: {
    title: "Operación del dispositivo",
    manualWave: "Registrar onda manual",
    resetDevice: "Restablecer dispositivo",
    clearReport: "Limpiar informe",
    clearWave: "Limpiar registro de onda",
    executing: "Ejecutando...",
    selectOperation: "Por favor, seleccione la operación",
    success: {
      manualWave: "Registro de onda manual exitoso",
      resetDevice: "Restablecimiento de dispositivo exitoso",
      clearReport: "Limpiar informe exitoso",
      clearWave: "Limpiar registro de onda exitoso"
    },
    fail: {
      manualWave: "Registro de onda manual fallido, razón:",
      resetDevice: "Restablecimiento de dispositivo fallido, razón:",
      clearReport: "Limpiar informe fallido, razón:",
      clearWave: "Limpiar registro de onda fallido, razón:"
    }
  },
  time: {
    title: "Sincronización de tiempo del dispositivo",
    currentTime: "Tiempo actual",
    deviceTime: "Tiempo del dispositivo",
    selectDateTime: "Seleccionar fecha y hora",
    milliseconds: "Milisegundos",
    now: "Ahora",
    read: "Leer",
    write: "Escribir",
    readSuccess: "Leer tiempo del dispositivo exitoso.",
    readFailed: "Error al leer tiempo del dispositivo: {msg}",
    readFailedInvalidFormat: "Error al leer tiempo del dispositivo: Formato de tiempo no válido",
    readFailedDataError: "Error al leer tiempo del dispositivo: Error de formato de datos de tiempo",
    writeSuccess: "Escribir tiempo del dispositivo exitoso.",
    writeFailed: "Error al escribir tiempo del dispositivo: {msg}",
    writeFailedInvalidFormat: "Error al escribir tiempo del dispositivo: Formato de tiempo no válido",
    millisecondsRangeError: "El rango de milisegundos debe estar entre 0-999",
    unknownError: "Error desconocido"
  },
  reportOperate: {
    title: "Operación de informe",
    date: "Fecha:",
    search: "Buscar",
    save: "Guardar",
    clearList: "Limpiar lista",
    loading: "Cargando datos",
    progress: {
      title: "Información de progreso",
      loading: "Cargando",
      searching: "Buscando {type}"
    },
    table: {
      reportId: "Número de informe",
      name: "Nombre",
      time: "Tiempo",
      operationAddress: "Dirección de operación",
      operationParam: "Parámetro de operación",
      value: "Valor",
      step: "Paso",
      source: "Fuente",
      sourceType: "Tipo de fuente",
      result: "Resultado"
    },
    messages: {
      selectDateRange: "Por favor, seleccione un rango de fecha completo",
      noDataToSave: "No hay datos para guardar",
      saveSuccess: "Guardar exitoso",
      saveReport: "Guardar informe"
    }
  },
  reportGroup: {
    title: "Grupo de informe",
    date: "Fecha:",
    search: "Buscar",
    save: "Guardar",
    clearList: "Limpiar lista",
    autoRefresh: "Actualización automática",
    loading: "Cargando datos",
    progress: {
      title: "Información de progreso",
      loading: "Cargando",
      searching: "Buscando {type}"
    },
    table: {
      reportId: "Número de informe",
      time: "Tiempo",
      description: "Descripción"
    },
    contextMenu: {
      uploadWave: "Llamar onda",
      getHistoryReport: "Obtener informe histórico",
      saveResult: "Guardar resultado",
      clearContent: "Limpiar contenido de la página"
    },
    messages: {
      selectDateRange: "Por favor, seleccione un rango de fecha completo",
      noDataToSave: "No hay datos para guardar",
      noFileToUpload: "No hay archivo para llamar",
      saveSuccess: "Guardar exitoso",
      saveReport: "Guardar informe",
      waveToolNotConfigured: "No se configuró la ruta de herramienta de onda de terceros",
      waveFileUploading: "Llamando archivo de onda",
      waveFileUploadComplete: "Llamado de archivo de onda completado",
      waveFileUploadCompleteWithPath: "Llamado de archivo de onda completado, ruta: {path}",
      openWaveFileConfirm: "¿Desea abrir el archivo de onda con la herramienta de terceros?",
      openWaveFileTitle: "Sugerencia de precaución",
      confirm: "Confirmar",
      cancel: "Cancelar"
    },
    refresh: {
      stop: "Detener actualización",
      start: "Actualizar automáticamente"
    },
    hiddenItems: {
      show: "Mostrar elementos ocultos",
      hide: "No mostrar elementos ocultos"
    }
  },
  fileDownload: {
    title: "Descarga de archivo",
    deviceDirectory: "Directorio del dispositivo",
    reboot: "Reiniciar",
    noReboot: "No reiniciar",
    selectFile: "Seleccionar archivo",
    addDownloadFile: "Agregar archivo para descargar",
    addDownloadFolder: "Agregar carpeta para descargar",
    addDownloadFilesAndFolders: "Agregar archivos y carpetas",
    downloadFile: "Descargar archivo",
    cancelDownload: "Cancelar descarga",
    importList: "Importar lista",
    exportList: "Exportar lista",
    batchDelete: "Eliminar por lotes",
    clearList: "Limpiar lista",
    download: "Descargar",
    delete: "Eliminar",
    fileName: "Nombre de archivo",
    fileSize: "Tamaño de archivo",
    filePath: "Ruta de archivo",
    lastModified: "Última modificación",
    progress: "Progreso",
    status: "Estado",
    operation: "Operación",
    folder: "Carpeta",
    waitingDownload: "Esperando descarga",
    calculatingFileInfo: "Calcular información de archivo",
    downloadPreparing: "Preparando descarga",
    downloading: "Descargando......",
    downloadComplete: "Descarga completada",
    downloadError: "Error al descargar:",
    userCancelled: "Usuario canceló",
    allFilesComplete: "Descarga completada",
    fileExists: "El archivo {path} ya existe, no se puede agregar",
    selectValidFile: "Por favor, seleccione un archivo válido para la operación de descarga",
    remotePathEmpty: "La ruta remota no puede estar vacía",
    noDownloadTask: "No se pudo obtener la tarea de descarga para cancelar",
    downloadCancelled: "Descarga cancelada de archivo {path} completada",
    downloadCancelledFailed: "Descarga cancelada de archivo {path} fallida, razón: {msg}",
    fileDeleted: "Archivo {path} eliminado completamente",
    exportSuccess: "Exportación de lista de descarga de archivo exitosa",
    exportFailed: "Exportación de lista de descarga de archivo fallida",
    importSuccess: "Importación de lista de descarga de archivo exitosa",
    importFailed: "Importación de lista de descarga de archivo fallida: {msg}",
    downloadList: "Lista de archivos de descarga",
    exportTitle: "Exportar lista de archivos de descarga",
    importTitle: "Importar lista de archivos de descarga",
    error: "Error",
    tip: "Aviso",
    confirm: "Confirmar",
    sequence: "Índice",
    confirmButton: "Confirmar",
    cancelButton: "Cancelar",
    alertTitle: "Aviso",
    errorTitle: "Error",
    successTitle: "Éxito",
    warningTitle: "Advertencia",
    loading: "Cargando",
    executing: "Ejecutando...",
    noData: "Sin datos",
    selectDateRange: "Por favor seleccione el rango de fechas",
    search: "Buscar",
    save: "Guardar",
    clear: "Limpiar",
    refresh: "Actualizar",
    stop: "Detener",
    start: "Iniciar",
    show: "Mostrar",
    hide: "Ocultar",
    showHiddenItems: "Mostrar elementos ocultos",
    hideHiddenItems: "Ocultar elementos",
    continue: "Continuar",
    cancel: "Cancelar",
    confirmImport: "Confirmar importación",
    confirmExport: "Confirmar exportación",
    confirmDelete: "Confirmar eliminación",
    confirmClear: "Confirmar limpieza",
    confirmCancel: "Confirmar cancelación",
    confirmContinue: "Confirmar continuación",
    confirmStop: "Confirmar detención",
    confirmStart: "Confirmar inicio",
    confirmShow: "Confirmar mostrar",
    confirmHide: "Confirmar ocultar",
    confirmRefresh: "Confirmar actualización",
    confirmSave: "Confirmar guardado",
    confirmSearch: "Confirmar búsqueda",
    confirmClearList: "Confirmar limpieza de lista",
    confirmImportList: "Confirmar importación de lista",
    confirmExportList: "Confirmar exportación de lista",
    confirmBatchDelete: "Confirmar eliminación por lotes",
    confirmDownload: "Confirmar descarga",
    confirmCancelDownload: "Confirmar cancelación de descarga",
    confirmDeleteFile: "Confirmar eliminación de archivo",
    confirmClearFiles: "Confirmar limpieza de archivos",
    confirmImportFiles: "Confirmar importación de archivos",
    confirmExportFiles: "Confirmar exportación de archivos",
    confirmBatchDeleteFiles: "Confirmar eliminación por lotes de archivos",
    confirmDownloadFiles: "Confirmar descarga de archivos",
    confirmCancelDownloadFiles: "Confirmar cancelación de descarga de archivos",
    rename: "Renombrar para descarga",
    renamePlaceholder: "Renombrar al descargar (opcional)",
    renameCopyFailed: "Error al copiar archivo para renombrar:",
    packageProgram: "Empaquetado de programa",
    selectSaveDir: "Seleccionar directorio de guardado",
    packageBtn: "Empaquetar",
    locateDir: "Ubicar carpeta",
    saveDirEmpty: "¡Por favor seleccione primero el directorio de guardado!",
    packageSuccess: "¡Empaquetado de programa completado!",
    packageFailed: "Error de empaquetado: {msg}",
    noFileSelected: "¡Por favor seleccione los archivos a empaquetar!",
    zipPath: "Ruta del archivo ZIP: {zipPath}",
    addToDownload: "Agregar a descarga",
    fileAdded: "Archivo agregado a la lista de descarga: {path}",
    rebootSuccess: "Reinicio del dispositivo exitoso",
    rebootFailed: "Reinicio del dispositivo fallido: {msg}"
  },
  fileUpload: {
    serialNumber: "Índice",
    title: "Subida de archivo",
    importList: "Importar lista",
    exportList: "Exportar lista",
    batchDelete: "Eliminar por lotes",
    clearList: "Limpiar lista",
    upload: "Subir",
    cancelUpload: "Cancelar subida",
    delete: "Eliminar",
    sequence: "Índice",
    fileName: "Nombre de archivo",
    fileSize: "Tamaño de archivo",
    filePath: "Ruta de archivo",
    lastModified: "Última modificación",
    progress: "Progreso",
    statusTitle: "Estado",
    status: {
      waiting: "Esperando subir",
      preparing: "Preparando subir",
      uploading: "Subiendo......",
      completed: "Subida completada",
      error: "Error al subir:",
      cancelled: "Usuario canceló"
    },
    operation: "Operación",
    calculatingFileInfo: "Calcular información de archivo",
    uploadPreparing: "Preparando subir",
    uploading: "Subiendo......",
    uploadComplete: "Subida completada",
    uploadError: "Error al subir: {errorMsg}",
    userCancelled: "Usuario canceló",
    allFilesComplete: "Subida completada",
    fileExists: "El archivo {path} ya existe, no se puede agregar",
    invalidFile: "Por favor, seleccione un archivo válido para la operación de subida",
    emptySavePath: "La ruta de guardado de archivo no puede estar vacía",
    fileUploadComplete: "Archivo {fileName} subido completamente",
    selectPath: "Seleccionar ruta",
    pathOptions: {
      shr: "/shr",
      configuration: "/shr/configuration",
      log: "/log",
      wave: "/wave",
      comtrade: "/wave/comtrade"
    },
    deviceDirectory: "Directorio del dispositivo",
    savePath: "Ruta de guardado",
    setPath: "Establecer ruta",
    getFiles: "Obtener archivos",
    uploadFiles: "Subir archivos",
    errors: {
      invalidFile: "Por favor, seleccione un archivo válido para la operación de subida",
      emptySavePath: "La ruta de guardado de archivo no puede estar vacía",
      noUploadTask: "No se pudo obtener la tarea de subida para cancelar",
      getFilesFailed: "Error al obtener archivos del directorio del dispositivo"
    },
    messages: {
      uploadCompleted: "Archivo subido completamente",
      uploadCancelled: "Subida cancelada completamente",
      clearListSuccess: "Lista de archivos limpiada exitosamente"
    }
  },
  info: {
    title: "Información del dispositivo",
    export: "Exportar",
    exportSuccess: "Exportación de información básica del dispositivo exitosa",
    exportFailed: "Exportación de información básica del dispositivo fallida",
    exportTip: "Sugerencia",
    confirm: "Confirmar",
    exportLoading: "Exportando información básica del dispositivo...",
    getInfoFailed: "Error al obtener información del dispositivo. Razón:",
    dataEmpty: "Datos vacíos!"
  },
  summary: {
    title: "Resumen de grupo de dispositivos",
    basicInfo: "Info básica",
    settingTotal: "Total de valores",
    telemetry: "Telemetría",
    teleindication: "Teleindicación",
    telecontrol: "Telecontrol",
    driveOutput: "Salida de accionamiento",
    settingRatio: "Proporción de valores"
  },
  dict: {
    refresh: "Actualizar",
    confirm: "Confirmar",
    import: "Importar",
    export: "Exportar",
    sequence: "Índice",
    shortAddress: "Dirección corta",
    shortAddressTooltip: "Ingrese la dirección corta para buscar",
    chinese: "Chino",
    english: "Inglés",
    spanish: "Español",
    french: "Francés",
    operation: "Operación",
    confirmLog: "Confirmar diccionario",
    importLog: "Importar diccionario",
    exportLog: "Exportar diccionario",
    refreshLog: "Actualizar diccionario",
    newValueLog: "Nuevo valor:"
  },
  allParamCompare: {
    title: "Comparar diferencia de importación de todos los valores",
    cancel: "Cancelar",
    confirm: "Confirmar importación",
    groupName: "Nombre de grupo",
    name: "Nombre",
    description: "Descripción",
    minValue: "Valor mínimo",
    maxValue: "Valor máximo",
    step: "Paso",
    unit: "Unidad",
    address: "Dirección",
    oldValue: "Valor antiguo",
    newValue: "Nuevo valor",
    sequence: "Índice",
    searchName: "Ingrese el nombre del valor para buscar",
    searchDescription: "Ingrese la descripción del valor para buscar",
    messages: {
      noSelection: "No se seleccionó ningún dato",
      error: "Error"
    }
  },
  deviceForm: {
    title: {
      add: "Agregar dispositivo",
      edit: "Editar dispositivo"
    },
    name: "Nombre del dispositivo",
    ip: "IP",
    port: "Puerto",
    connectTimeout: "Tiempo de conexión (milisegundos)",
    readTimeout: "Tiempo de solicitud global (milisegundos)",
    paramTimeout: "Tiempo de modificación de valor (milisegundos)",
    encrypted: "Conexión cifrada",
    advanced: {
      show: "Mostrar opciones avanzadas",
      hide: "Ocultar opciones avanzadas"
    },
    buttons: {
      cancel: "Cancelar",
      confirm: "Confirmar"
    },
    messages: {
      nameRequired: "Por favor, ingrese el nombre del dispositivo",
      nameTooLong: "El nombre del dispositivo no debe ser demasiado largo",
      invalidIp: "Por favor, ingrese una IP válida",
      invalidPort: "El puerto debe estar entre 1-65535",
      timeoutTooShort: "El tiempo de espera no debe ser demasiado corto"
    }
  },
  paramCompare: {
    title: "Comparar diferencia de importación de valor",
    cancel: "Cancelar",
    confirm: "Confirmar importación",
    sequence: "Índice",
    name: "Nombre",
    description: "Descripción",
    minValue: "Valor mínimo",
    maxValue: "Valor máximo",
    step: "Paso",
    unit: "Unidad",
    address: "Dirección",
    oldValue: "Valor antiguo",
    newValue: "Nuevo valor",
    searchName: "Ingrese el nombre del valor para buscar",
    searchDescription: "Ingrese la descripción del valor para buscar",
    messages: {
      noSelection: "No se seleccionó ningún dato",
      error: "Error"
    }
  },
  progress: {
    title: "Información de progreso",
    executing: "Ejecutando..."
  },
  remoteYm: {
    title: "Remoto de impulso",
    sequence: "Índice",
    shortAddress: "Dirección corta",
    description: "Descripción",
    value: "Valor",
    operation: "Operación",
    inputShortAddressFilter: "Ingrese la dirección corta para filtrar",
    inputDescriptionFilter: "Ingrese la descripción para filtrar",
    invalidData: "Valor {name} del valor {value} no válido",
    error: "Error",
    success: "Éxito",
    executeSuccess: "Ejecución exitosa",
    prompt: "Aviso",
    confirmButton: "Confirmar",
    confirmExecute: "Confirmar ejecución",
    confirm: "Confirmar",
    executeButton: "Ejecutar",
    cancelButton: "Cancelar"
  },
  remoteYt: {
    title: "Remoto de ajuste",
    sequence: "Índice",
    directControl: "Control directo",
    selectControl: "Control de selección",
    shortAddress: "Dirección corta",
    description: "Descripción",
    value: "Valor",
    operation: "Operación",
    inputShortAddressFilter: "Ingrese la dirección corta para filtrar",
    inputDescriptionFilter: "Ingrese la descripción para filtrar",
    invalidData: "Valor {name} del valor {value} no válido",
    error: "Error",
    success: "Éxito",
    executeSuccess: "Ejecución exitosa",
    prompt: "Aviso",
    confirm: "Confirmar",
    errorInfo: "Información de error",
    executeFailed: "Fallo en la ejecución del telecontrol remoto, razón: {msg}",
    executeSuccessLog: "{desc} Ejecución del telecontrol remoto exitosa",
    cancelSuccess: "Cancelación exitosa",
    cancelFailed: "Fallo en la cancelación del telecontrol remoto, razón: {msg}",
    selectSuccess: "Selección exitosa, ¿ejecutar?",
    confirmInfo: "Información de confirmación",
    execute: "Ejecutar",
    cancel: "Cancelar"
  },
  paramSetting: {
    title: "Valor de parámetro del dispositivo",
    autoRefresh: "Actualización automática",
    refresh: "Actualizar",
    confirm: "Confirmar",
    import: "Importar",
    export: "Exportar",
    currentEditArea: "Área de operación actual",
    selectEditArea: "Área de edición actual",
    noDataToImport: "No hay datos para importar",
    noDataToConfirm: "No hay datos para confirmar",
    importSuccess: "Importación de valor exitosa",
    importFailed: "Importación de valor fallida",
    updateSuccess: "Actualización de valor exitosa",
    updateFailed: "Actualización de valor fallida",
    requestFailed: "Solicitud fallida, por favor intente más tarde",
    setEditArea: "Establecer",
    setEditAreaTitle: "Establecer área de edición",
    setEditAreaSuccess: "Área de edición establecida exitosamente",
    modifiedWarning: "Existen cambios no guardados, ¿desea continuar actualizando?",
    autoRefreshWarning: "Existen cambios no guardados, ¿desea continuar actualizando automáticamente?",
    autoRefreshDisabled: "No se permite modificar datos cuando la actualización automática está habilitada",
    invalidValue: "Valor {name} del valor {value} no válido",
    exportSuccess: "Exportación de valor de parámetro de dispositivo exitosa",
    exportFailed: "Exportación de valor de parámetro de dispositivo fallida",
    noDiffData: "No se obtuvo datos de diferencia",
    table: {
      index: "Índice",
      name: "Nombre",
      description: "Descripción",
      value: "Valor",
      minValue: "Valor mínimo",
      maxValue: "Valor máximo",
      step: "Paso",
      address: "Dirección",
      unit: "Unidad",
      operation: "Operación"
    },
    search: {
      namePlaceholder: "Ingrese el nombre del valor para buscar",
      descPlaceholder: "Ingrese la descripción del valor para buscar"
    }
  },
  remoteControl: {
    title: "Control remoto",
    sequence: "Índice",
    shortAddress: "Dirección corta",
    description: "Descripción",
    control: "Control de división/combinación",
    type: "Tipo",
    operation: "Operación",
    directControl: "Control directo",
    selectControl: "Control de selección",
    controlClose: "Control de división",
    controlOpen: "Control de combinación",
    noCheck: "No verificar",
    syncCheck: "Verificar sincronización",
    deadCheck: "Verificar sin presión",
    confirmInfo: "Confirmar información",
    execute: "Ejecutar",
    cancel: "Cancelar",
    success: "Éxito",
    failed: "Fallo",
    errorInfo: "Información de error",
    promptInfo: "Información de sugerencia",
    confirmSuccess: "Selección exitosa, ¿ejecutar?",
    executeSuccess: "Ejecución exitosa",
    cancelSuccess: "Cancelación exitosa",
    executeFailed: "Ejecución de control remoto fallida, razón:",
    cancelFailed: "Cancelación de control remoto fallida, razón:",
    remoteExecuteSuccess: "Ejecución de control remoto exitosa",
    remoteCancelSuccess: "Cancelación de control remoto exitosa"
  },
  remoteDrive: {
    action: "Acción",
    executeSuccess: "Ejecución exitosa",
    executeFailed: "Ejecución fallida",
    prompt: "Información de sugerencia",
    error: "Información de error",
    confirm: "Confirmar",
    shortAddress: "Dirección corta",
    description: "Descripción",
    operation: "Operación",
    enterToFilter: "Ingrese la dirección corta para filtrar",
    enterToFilterDesc: "Ingrese la descripción para filtrar",
    actionSuccess: "Ejecución de acción exitosa",
    actionFailed: "Ejecución de acción fallida",
    failureReason: "Razón de falla",
    sequence: "Índice"
  },
  remoteSignal: {
    autoRefresh: "Actualización automática",
    refresh: "Actualizar",
    export: "Exportar",
    sequence: "Índice",
    name: "Nombre",
    description: "Descripción",
    value: "Valor",
    quality: "Calidad",
    searchName: "Ingrese el nombre para buscar",
    searchDesc: "Ingrese la descripción para buscar",
    searchValue: "Ingrese el valor para buscar",
    exportTitle: "Exportar información de señal de dispositivo",
    exportSuccess: "Exportación de información de señal de dispositivo exitosa",
    exportFailed: "Exportación de información de señal de dispositivo fallida",
    exportSuccessWithPath: "Exportación de información de señal de dispositivo exitosa,",
    exportFailedWithError: "Exportación de información de señal de dispositivo fallida:",
    invalidData: "Datos no válidos:",
    errorInDataCallback: "Error en procesamiento de devolución de datos:",
    errorFetchingData: "Error al obtener datos:"
  },
  remoteTelemetry: {
    autoRefresh: "Actualización automática",
    refresh: "Actualizar",
    export: "Exportar",
    sequence: "Índice",
    name: "Nombre",
    description: "Descripción",
    value: "Valor",
    unit: "Unidad",
    quality: "Calidad",
    searchName: "Ingrese el nombre para buscar",
    searchDesc: "Ingrese la descripción para buscar",
    searchValue: "Ingrese el valor para buscar",
    exportTitle: "Exportar información de estado de dispositivo",
    exportSuccess: "Exportación de información de estado de dispositivo exitosa",
    exportFailed: "Exportación de información de estado de dispositivo fallida",
    exportSuccessWithPath: "Exportación de información de estado de dispositivo exitosa,",
    exportFailedWithError: "Exportación de información de estado de dispositivo fallida:",
    confirm: "Confirmar",
    tip: "Sugerencia",
    exportFileName: "Información de estado de dispositivo",
    selectPathLog: "Seleccionar ruta:"
  },
  remote: {
    directControl: "Control directo",
    selectControl: "Control de selección",
    serialNumber: "Índice",
    shortAddress: "Dirección corta",
    description: "Descripción",
    value: "Valor",
    operation: "Operación",
    inputShortAddressFilter: "Ingrese la dirección corta para filtrar",
    inputDescriptionFilter: "Ingrese la descripción para filtrar",
    invalidData: "Valor {name} del valor {value} no válido",
    error: "Error",
    success: "Éxito",
    executeSuccess: "Ejecución exitosa",
    prompt: "Información de sugerencia",
    confirm: "Confirmar",
    errorInfo: "Información de error",
    executeFailed: "Ejecución de ajuste fallida, razón: {msg}",
    executeSuccessLog: "{desc} Ajuste de ajuste exitoso",
    cancelSuccess: "Cancelación exitosa",
    cancelFailed: "Ejecución de ajuste fallida, razón: {msg}",
    selectSuccess: "Selección exitosa, ¿ejecutar?",
    confirmInfo: "Confirmar información",
    execute: "Ejecutar",
    cancel: "Cancelar"
  },
  report: {
    uploadWave: "Llamar onda",
    searchHistory: "Obtener informe histórico",
    saveResult: "Guardar resultado",
    clearContent: "Limpiar contenido de la página",
    date: "Fecha",
    query: "Buscar",
    save: "Guardar",
    autoRefresh: "Actualizar automáticamente",
    stopRefresh: "Detener actualización",
    clearList: "Limpiar lista",
    progressInfo: "Información de progreso",
    loading: "Cargando datos",
    reportNo: "Número de informe",
    time: "Tiempo",
    description: "Descripción",
    noFileToUpload: "No hay archivo para llamar",
    uploadSuccess: "Archivo de onda llamado completamente",
    uploadPath: "Archivo de onda llamado completamente, ruta:",
    noDataToSave: "No hay datos para guardar",
    saveSuccess: "Guardar exitoso",
    saveReport: "Guardar informe",
    openWaveConfirm: "¿Desea abrir el archivo de onda con la herramienta de terceros?",
    confirm: "Confirmar",
    cancel: "Cancelar",
    waveToolNotConfigured: "No se configuró la ruta de herramienta de onda de terceros",
    pleaseSelectTimeRange: "Por favor, seleccione un rango de fecha completo",
    querying: "Buscando",
    reportNumber: "Número de informe",
    operationAddress: "Dirección de operación",
    operationParams: "Parámetros de operación",
    result: "Resultado",
    progress: "Información de progreso",
    loadingText: "Cargando",
    selectCompleteTimeRange: "Por favor, seleccione un rango de fecha completo",
    fileUploading: "Llamando archivo de onda",
    fileUploadComplete: "Archivo llamado completamente"
  },
  customMenu: {
    addMenu: "Agregar menú personalizado",
    editMenu: "Editar menú personalizado",
    deleteMenu: "Eliminar menú personalizado",
    addReport: "Agregar informe personalizado",
    editReport: "Editar informe personalizado",
    deleteReport: "Eliminar informe personalizado",
    menuName: "Nombre del grupo",
    menuDesc: "Descripción",
    reportName: "Nombre del informe",
    reportDesc: "Descripción",
    reportKeyword: "Palabra clave",
    reportInherit: "Heredar informe",
    inputMenuName: "Por favor ingrese el nombre del grupo",
    inputMenuDesc: "Por favor ingrese la descripción",
    inputReportName: "Por favor ingrese el nombre del informe",
    inputReportDesc: "Por favor ingrese la descripción",
    inputReportKeyword: "Por favor ingrese la palabra clave",
    selectReportInherit: "Por favor seleccione el informe a heredar",
    cancel: "Cancelar",
    confirm: "Confirmar",
    successAddMenu: "Menú personalizado agregado con éxito",
    successEditMenu: "Menú personalizado editado con éxito",
    successDeleteMenu: "Menú personalizado eliminado con éxito",
    successAddReport: "Informe personalizado agregado con éxito",
    successEditReport: "Informe personalizado editado con éxito",
    successDeleteReport: "Informe personalizado eliminado con éxito",
    errorAction: "Operación fallida",
    errorDelete: "Error al eliminar",
    confirmDeleteMenu: "¿Está seguro de que desea eliminar este menú personalizado?",
    confirmDeleteReport: "¿Está seguro de que desea eliminar este informe personalizado?",
    tip: "Sugerencia"
  },
  tree: {
    inputGroupName: "Por favor ingrese el nombre del grupo",
    expandAll: "Expandir todo",
    collapseAll: "Colapsar todo"
  }
};
