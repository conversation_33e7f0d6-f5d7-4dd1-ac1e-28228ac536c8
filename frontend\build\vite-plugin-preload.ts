/**
 * Vite 预加载优化插件
 * 优化资源预加载和缓存策略
 */

import type { Plugin } from 'vite';
import { resolve } from 'path';

interface PreloadOptions {
  // 预加载的文件模式
  include?: string[];
  // 排除的文件模式
  exclude?: string[];
  // 是否启用资源提示
  enableResourceHints?: boolean;
  // 是否启用模块预加载
  enableModulePreload?: boolean;
  // 缓存策略
  cacheStrategy?: 'aggressive' | 'normal' | 'conservative';
}

const defaultOptions: PreloadOptions = {
  include: [
    '**/*.vue',
    '**/*.ts',
    '**/*.js',
    '**/router/**',
    '**/stores/**',
    '**/components/**'
  ],
  exclude: [
    '**/node_modules/**',
    '**/*.test.*',
    '**/*.spec.*'
  ],
  enableResourceHints: true,
  enableModulePreload: true,
  cacheStrategy: 'normal'
};

export function vitePreloadPlugin(options: PreloadOptions = {}): Plugin {
  const opts = { ...defaultOptions, ...options };
  
  return {
    name: 'vite-plugin-preload',
    configResolved(config) {
      // 在开发模式下启用更激进的缓存策略
      if (config.command === 'serve') {
        opts.cacheStrategy = 'aggressive';
      }
    },
    
    generateBundle(options, bundle) {
      // 生成预加载资源列表
      const preloadAssets: string[] = [];
      const criticalAssets: string[] = [];
      
      Object.keys(bundle).forEach(fileName => {
        const chunk = bundle[fileName];
        
        if (chunk.type === 'chunk') {
          // 标识关键路径资源
          if (chunk.isEntry || chunk.name?.includes('vendor')) {
            criticalAssets.push(fileName);
          }
          
          // 标识预加载资源
          if (chunk.isDynamicEntry || chunk.name?.includes('async')) {
            preloadAssets.push(fileName);
          }
        }
      });
      
      // 生成预加载清单
      this.emitFile({
        type: 'asset',
        fileName: 'preload-manifest.json',
        source: JSON.stringify({
          critical: criticalAssets,
          preload: preloadAssets,
          strategy: opts.cacheStrategy,
          timestamp: Date.now()
        }, null, 2)
      });
    },
    
    transformIndexHtml(html, context) {
      if (!opts.enableResourceHints) return html;
      
      const resourceHints: string[] = [];
      
      // 添加DNS预解析
      resourceHints.push('<link rel="dns-prefetch" href="//fonts.googleapis.com">');
      resourceHints.push('<link rel="dns-prefetch" href="//cdn.jsdelivr.net">');
      
      // 添加关键资源预加载
      if (opts.enableModulePreload) {
        resourceHints.push(`
          <script>
            // 模块预加载优化
            if ('modulepreload' in HTMLLinkElement.prototype) {
              const preloadModule = (src) => {
                const link = document.createElement('link');
                link.rel = 'modulepreload';
                link.href = src;
                document.head.appendChild(link);
              };
              
              // 预加载核心模块
              preloadModule('/src/main.ts');
              preloadModule('/src/App.vue');
              preloadModule('/src/routers/index.ts');
            }
          </script>
        `);
      }
      
      // 添加缓存优化脚本
      resourceHints.push(`
        <script>
          // 启用激进缓存策略
          if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
              navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                  console.log('SW registered: ', registration);
                })
                .catch(registrationError => {
                  console.log('SW registration failed: ', registrationError);
                });
            });
          }
          
          // 预加载关键资源
          const preloadCriticalResources = () => {
            const criticalResources = [
              '/src/styles/var.scss',
              '/src/styles/reset.scss',
              '/src/styles/common.scss'
            ];
            
            criticalResources.forEach(resource => {
              const link = document.createElement('link');
              link.rel = 'preload';
              link.as = 'style';
              link.href = resource;
              document.head.appendChild(link);
            });
          };
          
          // 延迟执行预加载
          requestIdleCallback ? 
            requestIdleCallback(preloadCriticalResources) : 
            setTimeout(preloadCriticalResources, 100);
        </script>
      `);
      
      // 将资源提示插入到head标签中
      return html.replace(
        '<head>',
        `<head>\n${resourceHints.join('\n')}`
      );
    }
  };
}

/**
 * 创建缓存优化插件
 */
export function viteCacheOptimizationPlugin(): Plugin {
  return {
    name: 'vite-cache-optimization',
    
    configResolved(config) {
      // 优化构建缓存
      if (config.command === 'build') {
        // 启用持久化缓存
        config.build = config.build || {};
        config.build.rollupOptions = config.build.rollupOptions || {};
        config.build.rollupOptions.cache = true;
      }
    },
    
    buildStart() {
      // 清理过期缓存
      this.addWatchFile('package.json');
      this.addWatchFile('vite.config.ts');
    },
    
    generateBundle() {
      // 生成缓存清单
      const cacheManifest = {
        version: '1.0.0',
        timestamp: Date.now(),
        strategy: 'stale-while-revalidate',
        maxAge: 86400000, // 24小时
        resources: {
          static: ['*.css', '*.js', '*.woff2'],
          dynamic: ['*.vue', '*.ts'],
          exclude: ['*.map', '*.test.*']
        }
      };
      
      this.emitFile({
        type: 'asset',
        fileName: 'cache-manifest.json',
        source: JSON.stringify(cacheManifest, null, 2)
      });
    }
  };
}

/**
 * 创建资源压缩插件
 */
export function viteCompressionPlugin(): Plugin {
  return {
    name: 'vite-compression',
    
    generateBundle(options, bundle) {
      // 标识可压缩的资源
      Object.keys(bundle).forEach(fileName => {
        const chunk = bundle[fileName];
        
        if (chunk.type === 'chunk' && chunk.code) {
          // 为大文件添加压缩提示
          if (chunk.code.length > 100000) { // 100KB
            console.log(`📦 大文件检测: ${fileName} (${(chunk.code.length / 1024).toFixed(2)}KB)`);
          }
        }
      });
    }
  };
}
